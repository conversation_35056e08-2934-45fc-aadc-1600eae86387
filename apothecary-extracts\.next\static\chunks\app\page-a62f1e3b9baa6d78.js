(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{6971:(e,s,r)=>{Promise.resolve().then(r.bind(r,9336))},9336:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u});var t=r(5155),a=r(2115),l=r(5506);function i(e){let{onVerified:s}=e,[r,l]=(0,a.useState)(!1);(0,a.useEffect)(()=>{localStorage.getItem("ageVerified")?s():l(!0)},[s]);let i=e=>{e?(localStorage.setItem("ageVerified","true"),l(!1),s()):window.location.href="https://www.samhsa.gov/marijuana"};return r?(0,t.jsx)("div",{className:"fixed inset-0 bg-primary-800 bg-opacity-95 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-cream-50 rounded-xl p-8 max-w-md w-full text-center shadow-medium",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-serif font-bold text-primary-800 mb-2",children:"Age Verification Required"}),(0,t.jsx)("p",{className:"text-charcoal-700 text-sm leading-relaxed",children:"You must be 21 years of age or older to view this website and purchase cannabis products. Please verify your age to continue."})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("button",{onClick:()=>i(!0),className:"w-full bg-primary-800 text-cream-50 py-3 px-6 rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-offset-2",children:"Yes, I'm 21 or older"}),(0,t.jsx)("button",{onClick:()=>i(!1),className:"w-full bg-transparent text-charcoal-700 py-3 px-6 rounded-lg font-medium border border-charcoal-300 hover:bg-charcoal-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-charcoal-400 focus:ring-offset-2",children:"No, I'm under 21"})]}),(0,t.jsx)("div",{className:"mt-6 pt-4 border-t border-charcoal-200",children:(0,t.jsx)("p",{className:"text-xs text-charcoal-500 leading-relaxed",children:"By entering this website, you certify that you are of legal age to purchase cannabis products in your jurisdiction. Cannabis products have not been evaluated by the FDA and are not intended to diagnose, treat, cure, or prevent any disease."})})]})}):null}var o=r(6874),c=r.n(o);function n(){return(0,t.jsxs)("section",{className:"relative bg-gradient-to-br from-primary-800 via-primary-700 to-primary-600 text-cream-50 overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,t.jsx)("div",{className:"absolute inset-0 bg-primary-900"})}),(0,t.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,t.jsxs)("div",{className:"text-center lg:text-left",children:[(0,t.jsxs)("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-serif font-bold leading-tight mb-6",children:["Premium Cannabis",(0,t.jsx)("span",{className:"block text-gold-300",children:"Crafted with Care"})]}),(0,t.jsx)("p",{className:"text-xl lg:text-2xl text-cream-200 mb-8 leading-relaxed",children:"Discover Colorado's finest selection of cannabis products, expertly curated for quality, potency, and purity."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start",children:[(0,t.jsxs)(c(),{href:"/products",className:"inline-flex items-center justify-center px-8 py-4 bg-gold-500 text-primary-800 font-semibold rounded-lg hover:bg-gold-400 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:ring-offset-2 focus:ring-offset-primary-800",children:["Shop Products",(0,t.jsx)("svg",{className:"ml-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]}),(0,t.jsxs)(c(),{href:"/locations",className:"inline-flex items-center justify-center px-8 py-4 bg-transparent text-cream-50 font-semibold rounded-lg border-2 border-cream-50 hover:bg-cream-50 hover:text-primary-800 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cream-50 focus:ring-offset-2 focus:ring-offset-primary-800",children:["Find Locations",(0,t.jsxs)("svg",{className:"ml-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})]})]})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"aspect-square bg-gradient-to-br from-gold-400 to-gold-600 rounded-2xl shadow-medium p-8 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-32 h-32 mx-auto mb-4 bg-primary-800 rounded-full flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-16 h-16 text-gold-300",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})})}),(0,t.jsx)("h3",{className:"text-2xl font-serif font-bold text-primary-800 mb-2",children:"Quality Assured"}),(0,t.jsx)("p",{className:"text-primary-700",children:"Lab-tested for purity and potency"})]})}),(0,t.jsx)("div",{className:"absolute -top-4 -right-4 w-24 h-24 bg-sage-300 rounded-full opacity-80"}),(0,t.jsx)("div",{className:"absolute -bottom-4 -left-4 w-16 h-16 bg-gold-300 rounded-full opacity-60"})]})]})}),(0,t.jsx)("div",{className:"absolute bottom-0 left-0 right-0",children:(0,t.jsx)("svg",{viewBox:"0 0 1440 120",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,t.jsx)("path",{d:"M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0Z",fill:"#f8f6f0"})})})]})}let d=[{name:"Premium Flower",description:"Hand-selected, top-shelf cannabis flower with exceptional quality and potency.",image:"/api/placeholder/400/300",href:"/products/flower",features:["Lab Tested","Organic Grown","Various Strains"],color:"from-primary-600 to-primary-700"},{name:"Concentrates & Extracts",description:"Pure, potent concentrates including wax, shatter, and live resin.",image:"/api/placeholder/400/300",href:"/products/concentrates",features:["High Potency","Pure Extraction","Multiple Forms"],color:"from-gold-500 to-gold-600"},{name:"Edibles",description:"Delicious, precisely dosed edibles for a controlled cannabis experience.",image:"/api/placeholder/400/300",href:"/products/edibles",features:["Precise Dosing","Great Taste","Long Lasting"],color:"from-sage-400 to-sage-500"},{name:"Topicals",description:"Therapeutic cannabis topicals for localized relief and wellness.",image:"/api/placeholder/400/300",href:"/products/topicals",features:["Non-Psychoactive","Therapeutic","Natural Relief"],color:"from-cream-400 to-cream-500"}];function x(){return(0,t.jsx)("section",{className:"py-20 bg-cream-100",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-16",children:[(0,t.jsx)("h2",{className:"text-4xl font-serif font-bold text-primary-800 mb-4",children:"Our Product Categories"}),(0,t.jsx)("p",{className:"text-xl text-charcoal-600 max-w-3xl mx-auto leading-relaxed",children:"Explore our carefully curated selection of premium cannabis products, each category offering unique benefits and experiences."})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:d.map((e,s)=>(0,t.jsxs)("div",{className:"group bg-cream-50 rounded-xl overflow-hidden shadow-soft hover:shadow-medium transition-all duration-300 transform hover:-translate-y-2",children:[(0,t.jsxs)("div",{className:"h-48 bg-gradient-to-br ".concat(e.color," relative overflow-hidden"),children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300"}),(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,t.jsx)("div",{className:"text-center text-cream-50",children:(0,t.jsxs)("div",{className:"w-16 h-16 mx-auto mb-2 bg-cream-50 bg-opacity-20 rounded-full flex items-center justify-center",children:[0===s&&(0,t.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})}),1===s&&(0,t.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})}),2===s&&(0,t.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})}),3===s&&(0,t.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"})})]})})})]}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-primary-800 mb-2",children:e.name}),(0,t.jsx)("p",{className:"text-charcoal-600 text-sm mb-4 leading-relaxed",children:e.description}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:e.features.map(e=>(0,t.jsx)("span",{className:"px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full",children:e},e))}),(0,t.jsxs)(c(),{href:e.href,className:"inline-flex items-center text-primary-700 hover:text-primary-800 font-medium text-sm group-hover:underline transition-colors duration-200",children:["Explore ",e.name,(0,t.jsx)("svg",{className:"ml-1 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]},e.name))}),(0,t.jsx)("div",{className:"text-center mt-12",children:(0,t.jsxs)(c(),{href:"/products",className:"inline-flex items-center px-8 py-4 bg-primary-800 text-cream-50 font-semibold rounded-lg hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-offset-2",children:["View All Products",(0,t.jsx)("svg",{className:"ml-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})})]})})}var m=r(1225),h=r(2056);function u(){let[e,s]=(0,a.useState)(!1);return(0,t.jsxs)(t.Fragment,{children:[!e&&(0,t.jsx)(i,{onVerified:()=>s(!0)}),e&&(0,t.jsxs)("div",{className:"min-h-screen bg-cream-100",children:[(0,t.jsx)(l.A,{}),(0,t.jsxs)("main",{children:[(0,t.jsx)(n,{}),(0,t.jsx)("section",{className:"py-8 bg-cream-100",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsx)(h.A,{})})}),(0,t.jsx)(x,{}),(0,t.jsx)(m.A,{}),(0,t.jsx)("section",{className:"py-20 bg-primary-800",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,t.jsx)("h2",{className:"text-3xl font-serif font-bold text-cream-50 mb-4",children:"Stay Updated with Apothecary Extracts"}),(0,t.jsx)("p",{className:"text-xl text-cream-200 mb-8",children:"Get the latest news on new products, special offers, and cannabis education."}),(0,t.jsxs)("form",{className:"flex flex-col sm:flex-row gap-4 max-w-md mx-auto",children:[(0,t.jsx)("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-4 py-3 rounded-lg border border-cream-300 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:border-transparent text-charcoal-800",required:!0}),(0,t.jsx)("button",{type:"submit",className:"px-8 py-3 bg-gold-500 text-primary-800 font-semibold rounded-lg hover:bg-gold-400 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:ring-offset-2 focus:ring-offset-primary-800",children:"Subscribe"})]}),(0,t.jsx)("p",{className:"text-sm text-cream-300 mt-4",children:"We respect your privacy. Unsubscribe at any time."})]})})]}),(0,t.jsx)("footer",{className:"bg-charcoal-800 text-cream-100 py-16",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,t.jsx)("div",{className:"text-2xl font-serif font-bold text-cream-50 mb-4",children:"Apothecary Extracts"}),(0,t.jsx)("p",{className:"text-cream-300 mb-4 leading-relaxed",children:"Colorado's premier cannabis dispensary, committed to providing the highest quality products and exceptional customer service."}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)("a",{href:"#",className:"text-cream-300 hover:text-gold-400 transition-colors",children:[(0,t.jsx)("span",{className:"sr-only",children:"Facebook"}),(0,t.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})]}),(0,t.jsxs)("a",{href:"#",className:"text-cream-300 hover:text-gold-400 transition-colors",children:[(0,t.jsx)("span",{className:"sr-only",children:"Instagram"}),(0,t.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z"})})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-cream-50 mb-4",children:"Quick Links"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/products",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Products"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/locations",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Locations"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/about",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"About Us"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/education",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Education"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-cream-50 mb-4",children:"Legal"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/privacy",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Privacy Policy"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/terms",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Terms of Service"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/compliance",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Compliance"})})]})]})]}),(0,t.jsx)("div",{className:"border-t border-charcoal-600 mt-12 pt-8 text-center",children:(0,t.jsx)("p",{className:"text-cream-400 text-sm",children:"\xa9 2025 Apothecary Extracts. All rights reserved. | Licensed Cannabis Retailer | Must be 21+ to purchase"})})]})})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[874,355,441,684,358],()=>s(6971)),_N_E=e.O()}]);