(()=>{var e={};e.id=571,e.ids=[571],e.modules={440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var a=t(1658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3087:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>l});var a=t(7413),s=t(5230),i=t.n(s),n=t(6755),o=t.n(n);t(1135);let l={title:"Apothecary Extracts - Premium Cannabis Dispensary | Colorado Springs",description:"Discover Colorado's finest selection of premium cannabis products at Apothecary Extracts. Quality flower, concentrates, edibles, and topicals. Medical and recreational cannabis dispensary in Colorado Springs.",keywords:"cannabis dispensary, marijuana, Colorado Springs, premium cannabis, flower, concentrates, edibles, topicals, medical marijuana, recreational cannabis",openGraph:{title:"Apothecary Extracts - Premium Cannabis Dispensary",description:"Colorado's finest selection of premium cannabis products. Quality, potency, and purity guaranteed.",type:"website",locale:"en_US"},robots:{index:!0,follow:!0}};function c({children:e}){return(0,a.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,a.jsx)("body",{className:`${i().variable} ${o().variable} antialiased`,children:e})})}},5613:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var a=t(687),s=t(3210),i=t(6246);let n=[{id:1,name:"Blue Dream",category:"flower",type:"Hybrid",thc:"18-22%",cbd:"0.1-0.3%",price:45,priceUnit:"1/8 oz",image:"/api/placeholder/300/300",description:"A balanced hybrid with sweet berry aroma and cerebral, full-body effects.",effects:["Creative","Euphoric","Relaxed"],flavors:["Berry","Sweet","Vanilla"],inStock:!0,featured:!0},{id:2,name:"Live Resin Cart - OG Kush",category:"concentrates",type:"Indica",thc:"85-90%",cbd:"0.1%",price:65,priceUnit:"0.5g",image:"/api/placeholder/300/300",description:"Premium live resin cartridge with authentic OG Kush flavor profile.",effects:["Relaxed","Sleepy","Happy"],flavors:["Earthy","Pine","Lemon"],inStock:!0,featured:!1},{id:3,name:"Gummy Bears - Mixed Berry",category:"edibles",type:"Hybrid",thc:"10mg each",cbd:"0mg",price:25,priceUnit:"10-pack",image:"/api/placeholder/300/300",description:"Delicious mixed berry gummies with precise 10mg THC dosing.",effects:["Happy","Relaxed","Euphoric"],flavors:["Berry","Sweet","Fruity"],inStock:!0,featured:!0},{id:4,name:"Pain Relief Balm",category:"topicals",type:"CBD",thc:"0mg",cbd:"200mg",price:35,priceUnit:"2oz jar",image:"/api/placeholder/300/300",description:"Non-psychoactive topical balm for localized pain and inflammation relief.",effects:["Pain Relief","Anti-inflammatory","Soothing"],flavors:["Eucalyptus","Menthol","Lavender"],inStock:!0,featured:!1}],o=[{id:"all",name:"All Products",count:n.length},{id:"flower",name:"Flower",count:n.filter(e=>"flower"===e.category).length},{id:"concentrates",name:"Concentrates",count:n.filter(e=>"concentrates"===e.category).length},{id:"edibles",name:"Edibles",count:n.filter(e=>"edibles"===e.category).length},{id:"topicals",name:"Topicals",count:n.filter(e=>"topicals"===e.category).length}];function l(){let[e,r]=(0,s.useState)("all"),[t,l]=(0,s.useState)("name"),[c,d]=(0,s.useState)(""),m=n.filter(r=>("all"===e||r.category===e)&&(""===c||r.name.toLowerCase().includes(c.toLowerCase()))).sort((e,r)=>{switch(t){case"price-low":return e.price-r.price;case"price-high":return r.price-e.price;case"thc":return parseFloat(r.thc)-parseFloat(e.thc);default:return e.name.localeCompare(r.name)}});return(0,a.jsxs)("div",{className:"min-h-screen bg-cream-100",children:[(0,a.jsx)(i.A,{}),(0,a.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-4xl font-serif font-bold text-primary-800 mb-4",children:"Our Products"}),(0,a.jsx)("p",{className:"text-xl text-charcoal-600 max-w-3xl",children:"Explore our carefully curated selection of premium cannabis products, all lab-tested for quality and potency."})]}),(0,a.jsx)("div",{className:"bg-cream-50 rounded-xl p-6 mb-8 shadow-soft",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-primary-800 mb-3",children:"Categories"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:o.map(t=>(0,a.jsxs)("button",{onClick:()=>r(t.id),className:`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${e===t.id?"bg-primary-800 text-cream-50":"bg-cream-200 text-charcoal-700 hover:bg-primary-100"}`,children:[t.name," (",t.count,")"]},t.id))})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"search",className:"block text-sm font-medium text-charcoal-700 mb-1",children:"Search Products"}),(0,a.jsx)("input",{type:"text",id:"search",value:c,onChange:e=>d(e.target.value),placeholder:"Search by name...",className:"px-4 py-2 border border-charcoal-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-600 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"sort",className:"block text-sm font-medium text-charcoal-700 mb-1",children:"Sort By"}),(0,a.jsxs)("select",{id:"sort",value:t,onChange:e=>l(e.target.value),className:"px-4 py-2 border border-charcoal-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-600 focus:border-transparent",children:[(0,a.jsx)("option",{value:"name",children:"Name A-Z"}),(0,a.jsx)("option",{value:"price-low",children:"Price: Low to High"}),(0,a.jsx)("option",{value:"price-high",children:"Price: High to Low"}),(0,a.jsx)("option",{value:"thc",children:"THC Content"})]})]})]})]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:m.map(e=>(0,a.jsxs)("div",{className:"bg-cream-50 rounded-xl overflow-hidden shadow-soft hover:shadow-medium transition-all duration-300 transform hover:-translate-y-1",children:[(0,a.jsxs)("div",{className:"relative h-48 bg-gradient-to-br from-primary-100 to-primary-200",children:[e.featured&&(0,a.jsx)("div",{className:"absolute top-3 left-3 bg-gold-500 text-primary-800 px-2 py-1 rounded-full text-xs font-semibold",children:"Featured"}),(0,a.jsx)("div",{className:"absolute top-3 right-3",children:(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${e.inStock?"bg-sage-200 text-sage-800":"bg-charcoal-200 text-charcoal-700"}`,children:e.inStock?"In Stock":"Out of Stock"})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"mb-2",children:(0,a.jsx)("span",{className:"text-xs font-medium text-primary-600 uppercase tracking-wide",children:e.category})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-primary-800 mb-2",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-charcoal-600 mb-4 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("span",{className:"text-charcoal-500",children:"THC:"}),(0,a.jsx)("span",{className:"font-medium text-charcoal-800 ml-1",children:e.thc})]}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("span",{className:"text-charcoal-500",children:"CBD:"}),(0,a.jsx)("span",{className:"font-medium text-charcoal-800 ml-1",children:e.cbd})]})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:e.effects.slice(0,3).map(e=>(0,a.jsx)("span",{className:"px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full",children:e},e))})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"text-2xl font-bold text-primary-800",children:["$",e.price]}),(0,a.jsxs)("span",{className:"text-sm text-charcoal-500 ml-1",children:["/",e.priceUnit]})]}),(0,a.jsx)("button",{disabled:!e.inStock,className:`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${e.inStock?"bg-primary-800 text-cream-50 hover:bg-primary-700":"bg-charcoal-300 text-charcoal-500 cursor-not-allowed"}`,children:e.inStock?"View Details":"Out of Stock"})]})]})]},e.id))}),0===m.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-6xl text-charcoal-300 mb-4",children:"\uD83D\uDD0D"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-charcoal-700 mb-2",children:"No products found"}),(0,a.jsx)("p",{className:"text-charcoal-500",children:"Try adjusting your search or filter criteria."})]})]})]})}},5879:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=t(5239),s=t(8088),i=t(8170),n=t.n(i),o=t(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let c={children:["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8547)),"O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\products\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\products\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6215:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},6246:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var a=t(687),s=t(3210),i=t(5814),n=t.n(i);function o(){let[e,r]=(0,s.useState)(!1),t=[{name:"Home",href:"/"},{name:"Products",href:"/products"},{name:"Locations",href:"/locations"},{name:"About",href:"/about"},{name:"Education",href:"/education"},{name:"Contact",href:"/contact"}];return(0,a.jsxs)("nav",{className:"bg-cream-50 shadow-soft sticky top-0 z-40",children:[(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(n(),{href:"/",className:"flex items-center",children:(0,a.jsx)("div",{className:"text-2xl font-serif font-bold text-primary-800",children:"Apothecary Extracts"})})}),(0,a.jsx)("div",{className:"hidden md:block",children:(0,a.jsx)("div",{className:"ml-10 flex items-baseline space-x-8",children:t.map(e=>(0,a.jsx)(n(),{href:e.href,className:"text-charcoal-700 hover:text-primary-800 px-3 py-2 text-sm font-medium transition-colors duration-200",children:e.name},e.name))})}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsxs)("button",{onClick:()=>r(!e),className:"inline-flex items-center justify-center p-2 rounded-md text-charcoal-700 hover:text-primary-800 hover:bg-cream-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-600","aria-expanded":"false",children:[(0,a.jsx)("span",{className:"sr-only",children:"Open main menu"}),e?(0,a.jsx)("svg",{className:"block h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}):(0,a.jsx)("svg",{className:"block h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})]})})]})}),e&&(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-cream-100 border-t border-cream-200",children:t.map(e=>(0,a.jsx)(n(),{href:e.href,className:"text-charcoal-700 hover:text-primary-800 block px-3 py-2 text-base font-medium transition-colors duration-200",onClick:()=>r(!1),children:e.name},e.name))})})]})}},7245:()=>{},8467:(e,r,t)=>{Promise.resolve().then(t.bind(t,8547))},8547:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"O:\\\\VSCODE PROJECTS\\\\2025 NEW\\\\Apothecary\\\\apothecary-extracts\\\\src\\\\app\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\products\\page.tsx","default")},8715:(e,r,t)=>{Promise.resolve().then(t.bind(t,5613))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9629:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,210,567],()=>t(5879));module.exports=a})();