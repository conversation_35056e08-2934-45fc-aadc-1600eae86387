(()=>{var e={};e.id=453,e.ids=[453],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2323:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var t=r(5239),a=r(8088),l=r(8170),i=r.n(l),n=r(893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(s,o);let c={children:["",{children:["locations",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3171)),"O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\locations\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\locations\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/locations/page",pathname:"/locations",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3171:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"O:\\\\VSCODE PROJECTS\\\\2025 NEW\\\\Apothecary\\\\apothecary-extracts\\\\src\\\\app\\\\locations\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\locations\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6117:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(687),a=r(6246),l=r(5009),i=r(7499);function n(){return(0,t.jsxs)("div",{className:"min-h-screen bg-cream-100",children:[(0,t.jsx)(a.A,{}),(0,t.jsxs)("main",{children:[(0,t.jsx)("section",{className:"bg-gradient-to-br from-primary-800 to-primary-600 text-cream-50 py-16",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-4xl sm:text-5xl font-serif font-bold mb-4",children:"Our Locations"}),(0,t.jsx)("p",{className:"text-xl text-cream-200 max-w-3xl mx-auto",children:"Visit us at our convenient Colorado Springs locations for the full Apothecary Extracts experience."})]})})}),(0,t.jsx)("section",{className:"py-8 bg-cream-100",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsx)(i.A,{})})}),(0,t.jsx)(l.A,{}),(0,t.jsx)("section",{className:"py-16 bg-cream-50",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-3xl font-serif font-bold text-primary-800 mb-6",children:"What to Expect"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-6 h-6 bg-primary-800 rounded-full flex items-center justify-center mr-3 mt-1",children:(0,t.jsx)("svg",{className:"w-3 h-3 text-cream-50",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-primary-800 mb-1",children:"Expert Consultation"}),(0,t.jsx)("p",{className:"text-charcoal-600",children:"Our knowledgeable budtenders will help you find the perfect products for your needs."})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-6 h-6 bg-primary-800 rounded-full flex items-center justify-center mr-3 mt-1",children:(0,t.jsx)("svg",{className:"w-3 h-3 text-cream-50",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-primary-800 mb-1",children:"Premium Selection"}),(0,t.jsx)("p",{className:"text-charcoal-600",children:"Browse our extensive collection of lab-tested, high-quality cannabis products."})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-6 h-6 bg-primary-800 rounded-full flex items-center justify-center mr-3 mt-1",children:(0,t.jsx)("svg",{className:"w-3 h-3 text-cream-50",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-primary-800 mb-1",children:"Safe Environment"}),(0,t.jsx)("p",{className:"text-charcoal-600",children:"Clean, secure, and welcoming dispensary environment with strict safety protocols."})]})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-3xl font-serif font-bold text-primary-800 mb-6",children:"Visit Requirements"}),(0,t.jsx)("div",{className:"bg-gold-100 rounded-lg p-6",children:(0,t.jsxs)("ul",{className:"space-y-3 text-charcoal-700",children:[(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)("svg",{className:"w-5 h-5 text-gold-600 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"Must be 21+ years old"]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)("svg",{className:"w-5 h-5 text-gold-600 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"Valid government-issued ID required"]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)("svg",{className:"w-5 h-5 text-gold-600 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"Cash, debit, or CanPay accepted"]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)("svg",{className:"w-5 h-5 text-gold-600 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"Medical patients welcome"]})]})})]})]})})})]}),(0,t.jsx)("footer",{className:"bg-charcoal-800 text-cream-100 py-16",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-serif font-bold text-cream-50 mb-4",children:"Apothecary Extracts"}),(0,t.jsx)("p",{className:"text-cream-300 mb-4",children:"Colorado's premier cannabis dispensary"}),(0,t.jsx)("p",{className:"text-cream-400 text-sm",children:"\xa9 2025 Apothecary Extracts. All rights reserved. | Must be 21+ to purchase"})]})})})]})}},6859:(e,s,r)=>{Promise.resolve().then(r.bind(r,3171))},7475:(e,s,r)=>{Promise.resolve().then(r.bind(r,6117))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,210,567,540],()=>r(2323));module.exports=t})();