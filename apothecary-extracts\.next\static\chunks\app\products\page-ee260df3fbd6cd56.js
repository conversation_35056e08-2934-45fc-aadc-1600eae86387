(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[571],{1057:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>n});var s=r(5155),t=r(2115),c=r(5506);let i=[{id:1,name:"Blue Dream",category:"flower",type:"Hybrid",thc:"18-22%",cbd:"0.1-0.3%",price:45,priceUnit:"1/8 oz",image:"/api/placeholder/300/300",description:"A balanced hybrid with sweet berry aroma and cerebral, full-body effects.",effects:["Creative","Euphoric","Relaxed"],flavors:["Berry","Sweet","Vanilla"],inStock:!0,featured:!0},{id:2,name:"Live Resin Cart - OG Kush",category:"concentrates",type:"Indica",thc:"85-90%",cbd:"0.1%",price:65,priceUnit:"0.5g",image:"/api/placeholder/300/300",description:"Premium live resin cartridge with authentic OG Kush flavor profile.",effects:["Relaxed","Sleepy","Happy"],flavors:["Earthy","Pine","Lemon"],inStock:!0,featured:!1},{id:3,name:"Gummy Bears - Mixed Berry",category:"edibles",type:"Hybrid",thc:"10mg each",cbd:"0mg",price:25,priceUnit:"10-pack",image:"/api/placeholder/300/300",description:"Delicious mixed berry gummies with precise 10mg THC dosing.",effects:["Happy","Relaxed","Euphoric"],flavors:["Berry","Sweet","Fruity"],inStock:!0,featured:!0},{id:4,name:"Pain Relief Balm",category:"topicals",type:"CBD",thc:"0mg",cbd:"200mg",price:35,priceUnit:"2oz jar",image:"/api/placeholder/300/300",description:"Non-psychoactive topical balm for localized pain and inflammation relief.",effects:["Pain Relief","Anti-inflammatory","Soothing"],flavors:["Eucalyptus","Menthol","Lavender"],inStock:!0,featured:!1}],l=[{id:"all",name:"All Products",count:i.length},{id:"flower",name:"Flower",count:i.filter(e=>"flower"===e.category).length},{id:"concentrates",name:"Concentrates",count:i.filter(e=>"concentrates"===e.category).length},{id:"edibles",name:"Edibles",count:i.filter(e=>"edibles"===e.category).length},{id:"topicals",name:"Topicals",count:i.filter(e=>"topicals"===e.category).length}];function n(){let[e,a]=(0,t.useState)("all"),[r,n]=(0,t.useState)("name"),[o,d]=(0,t.useState)(""),m=i.filter(a=>("all"===e||a.category===e)&&(""===o||a.name.toLowerCase().includes(o.toLowerCase()))).sort((e,a)=>{switch(r){case"price-low":return e.price-a.price;case"price-high":return a.price-e.price;case"thc":return parseFloat(a.thc)-parseFloat(e.thc);default:return e.name.localeCompare(a.name)}});return(0,s.jsxs)("div",{className:"min-h-screen bg-cream-100",children:[(0,s.jsx)(c.A,{}),(0,s.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-4xl font-serif font-bold text-primary-800 mb-4",children:"Our Products"}),(0,s.jsx)("p",{className:"text-xl text-charcoal-600 max-w-3xl",children:"Explore our carefully curated selection of premium cannabis products, all lab-tested for quality and potency."})]}),(0,s.jsx)("div",{className:"bg-cream-50 rounded-xl p-6 mb-8 shadow-soft",children:(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-primary-800 mb-3",children:"Categories"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:l.map(r=>(0,s.jsxs)("button",{onClick:()=>a(r.id),className:"px-4 py-2 rounded-lg font-medium transition-colors duration-200 ".concat(e===r.id?"bg-primary-800 text-cream-50":"bg-cream-200 text-charcoal-700 hover:bg-primary-100"),children:[r.name," (",r.count,")"]},r.id))})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"search",className:"block text-sm font-medium text-charcoal-700 mb-1",children:"Search Products"}),(0,s.jsx)("input",{type:"text",id:"search",value:o,onChange:e=>d(e.target.value),placeholder:"Search by name...",className:"px-4 py-2 border border-charcoal-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-600 focus:border-transparent"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"sort",className:"block text-sm font-medium text-charcoal-700 mb-1",children:"Sort By"}),(0,s.jsxs)("select",{id:"sort",value:r,onChange:e=>n(e.target.value),className:"px-4 py-2 border border-charcoal-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-600 focus:border-transparent",children:[(0,s.jsx)("option",{value:"name",children:"Name A-Z"}),(0,s.jsx)("option",{value:"price-low",children:"Price: Low to High"}),(0,s.jsx)("option",{value:"price-high",children:"Price: High to Low"}),(0,s.jsx)("option",{value:"thc",children:"THC Content"})]})]})]})]})}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:m.map(e=>(0,s.jsxs)("div",{className:"bg-cream-50 rounded-xl overflow-hidden shadow-soft hover:shadow-medium transition-all duration-300 transform hover:-translate-y-1",children:[(0,s.jsxs)("div",{className:"relative h-48 bg-gradient-to-br from-primary-100 to-primary-200",children:[e.featured&&(0,s.jsx)("div",{className:"absolute top-3 left-3 bg-gold-500 text-primary-800 px-2 py-1 rounded-full text-xs font-semibold",children:"Featured"}),(0,s.jsx)("div",{className:"absolute top-3 right-3",children:(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(e.inStock?"bg-sage-200 text-sage-800":"bg-charcoal-200 text-charcoal-700"),children:e.inStock?"In Stock":"Out of Stock"})})]}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("div",{className:"mb-2",children:(0,s.jsx)("span",{className:"text-xs font-medium text-primary-600 uppercase tracking-wide",children:e.category})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-primary-800 mb-2",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-charcoal-600 mb-4 line-clamp-2",children:e.description}),(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsx)("span",{className:"text-charcoal-500",children:"THC:"}),(0,s.jsx)("span",{className:"font-medium text-charcoal-800 ml-1",children:e.thc})]}),(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsx)("span",{className:"text-charcoal-500",children:"CBD:"}),(0,s.jsx)("span",{className:"font-medium text-charcoal-800 ml-1",children:e.cbd})]})]}),(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:e.effects.slice(0,3).map(e=>(0,s.jsx)("span",{className:"px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full",children:e},e))})}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("span",{className:"text-2xl font-bold text-primary-800",children:["$",e.price]}),(0,s.jsxs)("span",{className:"text-sm text-charcoal-500 ml-1",children:["/",e.priceUnit]})]}),(0,s.jsx)("button",{disabled:!e.inStock,className:"px-4 py-2 rounded-lg font-medium transition-colors duration-200 ".concat(e.inStock?"bg-primary-800 text-cream-50 hover:bg-primary-700":"bg-charcoal-300 text-charcoal-500 cursor-not-allowed"),children:e.inStock?"View Details":"Out of Stock"})]})]})]},e.id))}),0===m.length&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-6xl text-charcoal-300 mb-4",children:"\uD83D\uDD0D"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-charcoal-700 mb-2",children:"No products found"}),(0,s.jsx)("p",{className:"text-charcoal-500",children:"Try adjusting your search or filter criteria."})]})]})]})}},5506:(e,a,r)=>{"use strict";r.d(a,{A:()=>l});var s=r(5155),t=r(2115),c=r(6874),i=r.n(c);function l(){let[e,a]=(0,t.useState)(!1),r=[{name:"Home",href:"/"},{name:"Products",href:"/products"},{name:"Locations",href:"/locations"},{name:"About",href:"/about"},{name:"Education",href:"/education"},{name:"Contact",href:"/contact"}];return(0,s.jsxs)("nav",{className:"bg-cream-50 shadow-soft sticky top-0 z-40",children:[(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(i(),{href:"/",className:"flex items-center",children:(0,s.jsx)("div",{className:"text-2xl font-serif font-bold text-primary-800",children:"Apothecary Extracts"})})}),(0,s.jsx)("div",{className:"hidden md:block",children:(0,s.jsx)("div",{className:"ml-10 flex items-baseline space-x-8",children:r.map(e=>(0,s.jsx)(i(),{href:e.href,className:"text-charcoal-700 hover:text-primary-800 px-3 py-2 text-sm font-medium transition-colors duration-200",children:e.name},e.name))})}),(0,s.jsx)("div",{className:"md:hidden",children:(0,s.jsxs)("button",{onClick:()=>a(!e),className:"inline-flex items-center justify-center p-2 rounded-md text-charcoal-700 hover:text-primary-800 hover:bg-cream-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-600","aria-expanded":"false",children:[(0,s.jsx)("span",{className:"sr-only",children:"Open main menu"}),e?(0,s.jsx)("svg",{className:"block h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}):(0,s.jsx)("svg",{className:"block h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})]})})]})}),e&&(0,s.jsx)("div",{className:"md:hidden",children:(0,s.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-cream-100 border-t border-cream-200",children:r.map(e=>(0,s.jsx)(i(),{href:e.href,className:"text-charcoal-700 hover:text-primary-800 block px-3 py-2 text-base font-medium transition-colors duration-200",onClick:()=>a(!1),children:e.name},e.name))})})]})}},8485:(e,a,r)=>{Promise.resolve().then(r.bind(r,1057))}},e=>{var a=a=>e(e.s=a);e.O(0,[874,441,684,358],()=>a(8485)),_N_E=e.O()}]);