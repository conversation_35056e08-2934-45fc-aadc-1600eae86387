(()=>{var t={};t.id=974,t.ids=[974],t.modules={846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1204:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>s});let s=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"O:\\\\VSCODE PROJECTS\\\\2025 NEW\\\\Apothecary\\\\apothecary-extracts\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\page.tsx","default")},1245:(t,e,i)=>{"use strict";let s;i.r(e),i.d(e,{default:()=>rN});var n,r,a=i(687),o=i(3210),l=i(6246);function h({onVerified:t}){let[e,i]=(0,o.useState)(!1),s=e=>{e?(localStorage.setItem("ageVerified","true"),i(!1),t()):window.location.href="https://www.samhsa.gov/marijuana"};return e?(0,a.jsx)("div",{className:"fixed inset-0 bg-primary-800 bg-opacity-95 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-cream-50 rounded-xl p-8 max-w-md w-full text-center shadow-medium",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-serif font-bold text-primary-800 mb-2",children:"Age Verification Required"}),(0,a.jsx)("p",{className:"text-charcoal-700 text-sm leading-relaxed",children:"You must be 21 years of age or older to view this website and purchase cannabis products. Please verify your age to continue."})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{onClick:()=>s(!0),className:"w-full bg-primary-800 text-cream-50 py-3 px-6 rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-offset-2",children:"Yes, I'm 21 or older"}),(0,a.jsx)("button",{onClick:()=>s(!1),className:"w-full bg-transparent text-charcoal-700 py-3 px-6 rounded-lg font-medium border border-charcoal-300 hover:bg-charcoal-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-charcoal-400 focus:ring-offset-2",children:"No, I'm under 21"})]}),(0,a.jsx)("div",{className:"mt-6 pt-4 border-t border-charcoal-200",children:(0,a.jsx)("p",{className:"text-xs text-charcoal-500 leading-relaxed",children:"By entering this website, you certify that you are of legal age to purchase cannabis products in your jurisdiction. Cannabis products have not been evaluated by the FDA and are not intended to diagnose, treat, cure, or prevent any disease."})})]})}):null}var u=i(5814),c=i.n(u);function d(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function p(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function m(t,e,i,s){if("function"==typeof e){let[n,r]=p(s);e=e(void 0!==i?i:t.custom,n,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,r]=p(s);e=e(void 0!==i?i:t.custom,n,r)}return e}function f(t,e,i){let s=t.getProps();return m(s,e,void 0!==i?i:s.custom,t)}function g(t,e){return t?.[e]??t?.default??t}let y=t=>t,v={},x=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],b={value:null,addProjectionMetrics:null};function w(t,e){let i=!1,s=!0,n={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,a=x.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,s=new Set,n=!1,r=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){a.has(e)&&(u.schedule(e),t()),l++,e(o)}let u={schedule:(t,e=!1,r=!1)=>{let o=r&&n?i:s;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{s.delete(t),a.delete(t)},process:t=>{if(o=t,n){r=!0;return}n=!0,[i,s]=[s,i],i.forEach(h),e&&b.value&&b.value.frameloop[e].push(l),l=0,i.clear(),n=!1,r&&(r=!1,u.process(t))}};return u}(r,e?i:void 0),t),{}),{setup:o,read:l,resolveKeyframes:h,preUpdate:u,update:c,preRender:d,render:p,postRender:m}=a,f=()=>{let r=v.useManualTiming?n.timestamp:performance.now();i=!1,v.useManualTiming||(n.delta=s?1e3/60:Math.max(Math.min(r-n.timestamp,40),1)),n.timestamp=r,n.isProcessing=!0,o.process(n),l.process(n),h.process(n),u.process(n),c.process(n),d.process(n),p.process(n),m.process(n),n.isProcessing=!1,i&&e&&(s=!1,t(f))},g=()=>{i=!0,s=!0,n.isProcessing||t(f)};return{schedule:x.reduce((t,e)=>{let s=a[e];return t[e]=(t,e=!1,n=!1)=>(i||g(),s.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<x.length;e++)a[x[e]].cancel(t)},state:n,steps:a}}let{schedule:P,cancel:T,state:S,steps:A}=w("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:y,!0),j=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],M=new Set(j),E=new Set(["width","height","top","left","right","bottom",...j]);function C(t,e){-1===t.indexOf(e)&&t.push(e)}function V(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class k{constructor(){this.subscriptions=[]}add(t){return C(this.subscriptions,t),()=>V(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,i);else for(let n=0;n<s;n++){let s=this.subscriptions[n];s&&s(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function D(){s=void 0}let R={now:()=>(void 0===s&&R.set(S.isProcessing||v.useManualTiming?S.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(D)}},L=t=>!isNaN(parseFloat(t)),N={current:void 0};class F{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=R.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=R.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=L(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new k);let i=this.events[t].add(e);return"change"===t?()=>{i(),P.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return N.current&&N.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=R.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function B(t,e){return new F(t,e)}let O=t=>Array.isArray(t),I=t=>!!(t&&t.getVelocity);function U(t,e){let i=t.getValue("willChange");if(I(i)&&i.add)return i.add(e);if(!i&&v.WillChange){let i=new v.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let W=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),$="data-"+W("framerAppearId"),z=(t,e)=>i=>e(t(i)),H=(...t)=>t.reduce(z),Y=(t,e,i)=>i>e?e:i<t?t:i,q=t=>1e3*t,X=t=>t/1e3,_={layout:0,mainThread:0,waapi:0},K=()=>{},G=()=>{},Z=t=>e=>"string"==typeof e&&e.startsWith(t),J=Z("--"),Q=Z("var(--"),tt=t=>!!Q(t)&&te.test(t.split("/*")[0].trim()),te=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ti={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},ts={...ti,transform:t=>Y(0,1,t)},tn={...ti,default:1},tr=t=>Math.round(1e5*t)/1e5,ta=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,to=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tl=(t,e)=>i=>!!("string"==typeof i&&to.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),th=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[n,r,a,o]=s.match(ta);return{[t]:parseFloat(n),[e]:parseFloat(r),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},tu=t=>Y(0,255,t),tc={...ti,transform:t=>Math.round(tu(t))},td={test:tl("rgb","red"),parse:th("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+tc.transform(t)+", "+tc.transform(e)+", "+tc.transform(i)+", "+tr(ts.transform(s))+")"},tp={test:tl("#"),parse:function(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:td.transform},tm=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tf=tm("deg"),tg=tm("%"),ty=tm("px"),tv=tm("vh"),tx=tm("vw"),tb={...tg,parse:t=>tg.parse(t)/100,transform:t=>tg.transform(100*t)},tw={test:tl("hsl","hue"),parse:th("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:s=1})=>"hsla("+Math.round(t)+", "+tg.transform(tr(e))+", "+tg.transform(tr(i))+", "+tr(ts.transform(s))+")"},tP={test:t=>td.test(t)||tp.test(t)||tw.test(t),parse:t=>td.test(t)?td.parse(t):tw.test(t)?tw.parse(t):tp.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?td.transform(t):tw.transform(t),getAnimatableNone:t=>{let e=tP.parse(t);return e.alpha=0,tP.transform(e)}},tT=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tS="number",tA="color",tj=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tM(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},n=[],r=0,a=e.replace(tj,t=>(tP.test(t)?(s.color.push(r),n.push(tA),i.push(tP.parse(t))):t.startsWith("var(")?(s.var.push(r),n.push("var"),i.push(t)):(s.number.push(r),n.push(tS),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:a,indexes:s,types:n}}function tE(t){return tM(t).values}function tC(t){let{split:e,types:i}=tM(t),s=e.length;return t=>{let n="";for(let r=0;r<s;r++)if(n+=e[r],void 0!==t[r]){let e=i[r];e===tS?n+=tr(t[r]):e===tA?n+=tP.transform(t[r]):n+=t[r]}return n}}let tV=t=>"number"==typeof t?0:tP.test(t)?tP.getAnimatableNone(t):t,tk={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(ta)?.length||0)+(t.match(tT)?.length||0)>0},parse:tE,createTransformer:tC,getAnimatableNone:function(t){let e=tE(t);return tC(t)(e.map(tV))}};function tD(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tR(t,e){return i=>i>0?e:t}let tL=(t,e,i)=>t+(e-t)*i,tN=(t,e,i)=>{let s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},tF=[tp,td,tw],tB=t=>tF.find(e=>e.test(t));function tO(t){let e=tB(t);if(K(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let i=e.parse(t);return e===tw&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let n=0,r=0,a=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,o=2*i-s;n=tD(o,s,t+1/3),r=tD(o,s,t),a=tD(o,s,t-1/3)}else n=r=a=i;return{red:Math.round(255*n),green:Math.round(255*r),blue:Math.round(255*a),alpha:s}}(i)),i}let tI=(t,e)=>{let i=tO(t),s=tO(e);if(!i||!s)return tR(t,e);let n={...i};return t=>(n.red=tN(i.red,s.red,t),n.green=tN(i.green,s.green,t),n.blue=tN(i.blue,s.blue,t),n.alpha=tL(i.alpha,s.alpha,t),td.transform(n))},tU=new Set(["none","hidden"]);function tW(t,e){return i=>tL(t,e,i)}function t$(t){return"number"==typeof t?tW:"string"==typeof t?tt(t)?tR:tP.test(t)?tI:tY:Array.isArray(t)?tz:"object"==typeof t?tP.test(t)?tI:tH:tR}function tz(t,e){let i=[...t],s=i.length,n=t.map((t,i)=>t$(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=n[e](t);return i}}function tH(t,e){let i={...t,...e},s={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(s[n]=t$(t[n])(t[n],e[n]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let tY=(t,e)=>{let i=tk.createTransformer(e),s=tM(t),n=tM(e);return s.indexes.var.length===n.indexes.var.length&&s.indexes.color.length===n.indexes.color.length&&s.indexes.number.length>=n.indexes.number.length?tU.has(t)&&!n.values.length||tU.has(e)&&!s.values.length?function(t,e){return tU.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):H(tz(function(t,e){let i=[],s={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let r=e.types[n],a=t.indexes[r][s[r]],o=t.values[a]??0;i[n]=o,s[r]++}return i}(s,n),n.values),i):(K(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),tR(t,e))};function tq(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tL(t,e,i):t$(t)(t,e)}let tX=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>P.update(e,t),stop:()=>T(e),now:()=>S.isProcessing?S.timestamp:R.now()}},t_=(t,e,i=10)=>{let s="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=Math.round(1e4*t(e/(n-1)))/1e4+", ";return`linear(${s.substring(0,s.length-2)})`};function tK(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tG(t,e,i){var s,n;let r=Math.max(e-5,0);return s=i-t(r),(n=e-r)?1e3/n*s:0}let tZ={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tJ(t,e){return t*Math.sqrt(1-e*e)}let tQ=["duration","bounce"],t0=["stiffness","damping","mass"];function t1(t,e){return e.some(e=>void 0!==t[e])}function t2(t=tZ.visualDuration,e=tZ.bounce){let i,s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:r}=s,a=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],l={done:!1,value:a},{stiffness:h,damping:u,mass:c,duration:d,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:tZ.velocity,stiffness:tZ.stiffness,damping:tZ.damping,mass:tZ.mass,isResolvedFromDuration:!1,...t};if(!t1(t,t0)&&t1(t,tQ))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,n=2*Y(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:tZ.mass,stiffness:s,damping:n}}else{let i=function({duration:t=tZ.duration,bounce:e=tZ.bounce,velocity:i=tZ.velocity,mass:s=tZ.mass}){let n,r;K(t<=q(tZ.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let a=1-e;a=Y(tZ.minDamping,tZ.maxDamping,a),t=Y(tZ.minDuration,tZ.maxDuration,X(t)),a<1?(n=e=>{let s=e*a,n=s*t;return .001-(s-i)/tJ(e,a)*Math.exp(-n)},r=e=>{let s=e*a*t,r=Math.pow(a,2)*Math.pow(e,2)*t,o=Math.exp(-s),l=tJ(Math.pow(e,2),a);return(s*i+i-r)*o*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let o=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(n,r,5/t);if(t=q(t),isNaN(o))return{stiffness:tZ.stiffness,damping:tZ.damping,duration:t};{let e=Math.pow(o,2)*s;return{stiffness:e,damping:2*a*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:tZ.mass}).isResolvedFromDuration=!0}return e}({...s,velocity:-X(s.velocity||0)}),f=p||0,g=u/(2*Math.sqrt(h*c)),y=o-a,v=X(Math.sqrt(h/c)),x=5>Math.abs(y);if(n||(n=x?tZ.restSpeed.granular:tZ.restSpeed.default),r||(r=x?tZ.restDelta.granular:tZ.restDelta.default),g<1){let t=tJ(v,g);i=e=>o-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>o-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),s=Math.min(t*e,300);return o-i*((f+g*v*y)*Math.sinh(s)+t*y*Math.cosh(s))/t}}let b={calculatedDuration:m&&d||null,next:t=>{let e=i(t);if(m)l.done=t>=d;else{let s=0===t?f:0;g<1&&(s=0===t?q(f):tG(i,t,e));let a=Math.abs(o-e)<=r;l.done=Math.abs(s)<=n&&a}return l.value=l.done?o:e,l},toString:()=>{let t=Math.min(tK(b),2e4),e=t_(e=>b.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return b}function t5({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:h=.5,restSpeed:u}){let c,d,p=t[0],m={done:!1,value:p},f=t=>void 0!==o&&t<o||void 0!==l&&t>l,g=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l,y=i*e,v=p+y,x=void 0===a?v:a(v);x!==v&&(y=x-p);let b=t=>-y*Math.exp(-t/s),w=t=>x+b(t),P=t=>{let e=b(t),i=w(t);m.done=Math.abs(e)<=h,m.value=m.done?x:i},T=t=>{f(m.value)&&(c=t,d=t2({keyframes:[m.value,g(m.value)],velocity:tG(w,t,m.value),damping:n,stiffness:r,restDelta:h,restSpeed:u}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,P(t),T(t)),void 0!==c&&t>=c)?d.next(t-c):(e||P(t),m)}}}t2.applyToOptions=t=>{let e=function(t,e=100,i){let s=i({...t,keyframes:[0,e]}),n=Math.min(tK(s),2e4);return{type:"keyframes",ease:t=>s.next(n*t).value/e,duration:X(n)}}(t,100,t2);return t.ease=e.ease,t.duration=q(e.duration),t.type="keyframes",t};let t4=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function t3(t,e,i,s){if(t===e&&i===s)return y;let n=e=>(function(t,e,i,s,n){let r,a,o=0;do(r=t4(a=e+(i-e)/2,s,n)-t)>0?i=a:e=a;while(Math.abs(r)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:t4(n(t),e,s)}let t8=t3(.42,0,1,1),t9=t3(0,0,.58,1),t6=t3(.42,0,.58,1),t7=t=>Array.isArray(t)&&"number"!=typeof t[0],et=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,ee=t=>e=>1-t(1-e),ei=t3(.33,1.53,.69,.99),es=ee(ei),en=et(es),er=t=>(t*=2)<1?.5*es(t):.5*(2-Math.pow(2,-10*(t-1))),ea=t=>1-Math.sin(Math.acos(t)),eo=ee(ea),el=et(ea),eh=t=>Array.isArray(t)&&"number"==typeof t[0],eu={linear:y,easeIn:t8,easeInOut:t6,easeOut:t9,circIn:ea,circInOut:el,circOut:eo,backIn:es,backInOut:en,backOut:ei,anticipate:er},ec=t=>"string"==typeof t,ed=t=>{if(eh(t)){G(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,i,s,n]=t;return t3(e,i,s,n)}return ec(t)?(G(void 0!==eu[t],`Invalid easing type '${t}'`,"invalid-easing-type"),eu[t]):t},ep=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s};function em({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){var n;let r=t7(s)?s.map(ed):ed(s),a={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:s,mixer:n}={}){let r=t.length;if(G(r===e.length,"Both input and output ranges must be the same length","range-length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let a=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let s=[],n=i||v.mix||tq,r=t.length-1;for(let i=0;i<r;i++){let r=n(t[i],t[i+1]);e&&(r=H(Array.isArray(e)?e[i]||y:e,r)),s.push(r)}return s}(e,s,n),l=o.length,h=i=>{if(a&&i<t[0])return e[0];let s=0;if(l>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let n=ep(t[s],t[s+1],i);return o[s](n)};return i?e=>h(Y(t[0],t[r-1],e)):h}((n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let n=ep(0,e,s);t.push(tL(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),e,{ease:Array.isArray(r)?r:e.map(()=>r||t6).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(a.value=o(e),a.done=e>=t,a)}}let ef=t=>null!==t;function eg(t,{repeat:e,repeatType:i="loop"},s,n=1){let r=t.filter(ef),a=n<0||e&&"loop"!==i&&e%2==1?0:r.length-1;return a&&void 0!==s?s:r[a]}let ey={decay:t5,inertia:t5,tween:em,keyframes:em,spring:t2};function ev(t){"string"==typeof t.type&&(t.type=ey[t.type])}class ex{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let eb=t=>t/100;class ew extends ex{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==R.now()&&this.tick(R.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},_.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;ev(t);let{type:e=em,repeat:i=0,repeatDelay:s=0,repeatType:n,velocity:r=0}=t,{keyframes:a}=t,o=e||em;o!==em&&"number"!=typeof a[0]&&(this.mixKeyframes=H(eb,tq(a[0],a[1])),a=[0,100]);let l=o({...t,keyframes:a});"mirror"===n&&(this.mirroredGenerator=o({...t,keyframes:[...a].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=tK(l));let{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:s,mixKeyframes:n,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:h,repeat:u,repeatType:c,repeatDelay:d,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>s;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let v=this.currentTime,x=i;if(u){let t=Math.min(this.currentTime,s)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,u+1))%2&&("reverse"===c?(i=1-i,d&&(i-=d/a)):"mirror"===c&&(x=r)),v=Y(0,1,i)*a}let b=y?{done:!1,value:h[0]}:x.next(v);n&&(b.value=n(b.value));let{done:w}=b;y||null===o||(w=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return P&&p!==t5&&(b.value=eg(h,this.options,f,this.speed)),m&&m(b.value),P&&this.finish(),b}then(t,e){return this.finished.then(t,e)}get duration(){return X(this.calculatedDuration)}get time(){return X(this.currentTime)}set time(t){t=q(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(R.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=X(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tX,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(R.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,_.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let eP=t=>180*t/Math.PI,eT=t=>eA(eP(Math.atan2(t[1],t[0]))),eS={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:eT,rotateZ:eT,skewX:t=>eP(Math.atan(t[1])),skewY:t=>eP(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},eA=t=>((t%=360)<0&&(t+=360),t),ej=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),eM=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),eE={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ej,scaleY:eM,scale:t=>(ej(t)+eM(t))/2,rotateX:t=>eA(eP(Math.atan2(t[6],t[5]))),rotateY:t=>eA(eP(Math.atan2(-t[2],t[0]))),rotateZ:eT,rotate:eT,skewX:t=>eP(Math.atan(t[4])),skewY:t=>eP(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eC(t){return+!!t.includes("scale")}function eV(t,e){let i,s;if(!t||"none"===t)return eC(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=eE,s=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=eS,s=e}if(!s)return eC(e);let r=i[e],a=s[1].split(",").map(eD);return"function"==typeof r?r(a):a[r]}let ek=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eV(i,e)};function eD(t){return parseFloat(t.trim())}let eR=t=>t===ti||t===ty,eL=new Set(["x","y","z"]),eN=j.filter(t=>!eL.has(t)),eF={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eV(e,"x"),y:(t,{transform:e})=>eV(e,"y")};eF.translateX=eF.x,eF.translateY=eF.y;let eB=new Set,eO=!1,eI=!1,eU=!1;function eW(){if(eI){let t=Array.from(eB).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eN.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eI=!1,eO=!1,eB.forEach(t=>t.complete(eU)),eB.clear()}function e$(){eB.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eI=!0)})}class ez{constructor(t,e,i,s,n,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(eB.add(this),eO||(eO=!0,P.read(e$),P.resolveKeyframes(eW))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;if(null===t[0]){let n=s?.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let s=i.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===n&&s.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eB.delete(this)}cancel(){"scheduled"===this.state&&(eB.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eH=t=>t.startsWith("--");function eY(t){let e;return()=>(void 0===e&&(e=t()),e)}let eq=eY(()=>void 0!==window.ScrollTimeline),eX={},e_=function(t,e){let i=eY(t);return()=>eX[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eK=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,eG={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eK([0,.65,.55,1]),circOut:eK([.55,0,1,.45]),backIn:eK([.31,.01,.66,-.59]),backOut:eK([.33,1.53,.69,.99])};function eZ(t){return"function"==typeof t&&"applyToOptions"in t}class eJ extends ex{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:s,pseudoElement:n,allowFlatten:r=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!n,this.allowFlatten=r,this.options=t,G("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return eZ(t)&&e_()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:s=0,duration:n=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:l}={},h){let u={[e]:i};l&&(u.offset=l);let c=function t(e,i){if(e)return"function"==typeof e?e_()?t_(e,i):"ease-out":eh(e)?eK(e):Array.isArray(e)?e.map(e=>t(e,i)||eG.easeOut):eG[e]}(o,n);Array.isArray(c)&&(u.easing=c),b.value&&_.waapi++;let d={delay:s,duration:n,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:r+1,direction:"reverse"===a?"alternate":"normal"};h&&(d.pseudoElement=h);let p=t.animate(u,d);return b.value&&p.finished.finally(()=>{_.waapi--}),p}(e,i,s,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=eg(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eH(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return X(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return X(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=q(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eq())?(this.animation.timeline=t,y):e(this)}}let eQ={anticipate:er,backInOut:en,circInOut:el};class e0 extends eJ{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eQ&&(t.ease=eQ[t.ease])}(t),ev(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:s,element:n,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let a=new ew({...r,autoplay:!1}),o=q(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let e1=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tk.test(t)||"0"===t)&&!t.startsWith("url("));function e2(t){return"object"==typeof t&&null!==t}function e5(t){return e2(t)&&"offsetHeight"in t}let e4=new Set(["opacity","clipPath","filter","transform"]),e3=eY(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class e8 extends ex{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:r="loop",keyframes:a,name:o,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=R.now();let c={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:r,name:o,motionValue:l,element:h,...u},d=h?.KeyframeResolver||ez;this.keyframeResolver=new d(a,(t,e,i)=>this.onKeyframesResolved(t,e,c,!i),o,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,s){this.keyframeResolver=void 0;let{name:n,type:r,velocity:a,delay:o,isHandoff:l,onUpdate:h}=i;this.resolvedAt=R.now(),!function(t,e,i,s){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],a=e1(n,e),o=e1(r,e);return K(a===o,`You are trying to animate ${e} from "${n}" to "${r}". "${a?r:n}" is not an animatable value.`,"value-not-animatable"),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||eZ(i))&&s)}(t,n,r,a)&&((v.instantAnimations||!o)&&h?.(eg(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let u={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},c=!l&&function(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:r,type:a}=t;if(!e5(e?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return e3()&&i&&e4.has(i)&&("transform"!==i||!l)&&!o&&!s&&"mirror"!==n&&0!==r&&"inertia"!==a}(u)?new e0({...u,element:u.motionValue.owner.current}):new ew(u);c.finished.then(()=>this.notifyFinished()).catch(y),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eU=!0,e$(),eW(),eU=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e9=t=>null!==t,e6={type:"spring",stiffness:500,damping:25,restSpeed:10},e7=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),it={type:"keyframes",duration:.8},ie={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ii=(t,{keyframes:e})=>e.length>2?it:M.has(t)?t.startsWith("scale")?e7(e[1]):e6:ie,is=(t,e,i,s={},n,r)=>a=>{let o=g(s,t)||{},l=o.delay||s.delay||0,{elapsed:h=0}=s;h-=q(l);let u={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...o,delay:-h,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:t,motionValue:e,element:r?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(o)&&Object.assign(u,ii(t,u)),u.duration&&(u.duration=q(u.duration)),u.repeatDelay&&(u.repeatDelay=q(u.repeatDelay)),void 0!==u.from&&(u.keyframes[0]=u.from);let c=!1;if(!1!==u.type&&(0!==u.duration||u.repeatDelay)||(u.duration=0,0===u.delay&&(c=!0)),(v.instantAnimations||v.skipAnimations)&&(c=!0,u.duration=0,u.delay=0),u.allowFlatten=!o.type&&!o.ease,c&&!r&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(e9),r=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[r]}(u.keyframes,o);if(void 0!==t)return void P.update(()=>{u.onUpdate(t),u.onComplete()})}return o.isSync?new ew(u):new e8(u)};function ir(t,e,{delay:i=0,transitionOverride:s,type:n}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:a,...o}=e;s&&(r=s);let l=[],h=n&&t.animationState&&t.animationState.getState()[n];for(let e in o){let s=t.getValue(e,t.latestValues[e]??null),n=o[e];if(void 0===n||h&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(h,e))continue;let a={delay:i,...g(r||{},e)},u=s.get();if(void 0!==u&&!s.isAnimating&&!Array.isArray(n)&&n===u&&!a.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){let i=t.props[$];if(i){let t=window.MotionHandoffAnimation(i,e,P);null!==t&&(a.startTime=t,c=!0)}}U(t,e),s.start(is(e,s,n,t.shouldReduceMotion&&E.has(e)?{type:!1}:a,t,c));let d=s.animation;d&&l.push(d)}return a&&Promise.all(l).then(()=>{P.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:s={},...n}=f(t,e)||{};for(let e in n={...n,...i}){var r;let i=O(r=n[e])?r[r.length-1]||0:r;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,B(i))}}(t,a)})}),l}function ia(t,e,i={}){let s=f(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(n=i.transitionOverride);let r=s?()=>Promise.all(ir(t,s,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:r=0,staggerChildren:a,staggerDirection:o}=n;return function(t,e,i=0,s=0,n=0,r=1,a){let o=[],l=t.variantChildren.size,h=(l-1)*n,u="function"==typeof s,c=u?t=>s(t,l):1===r?(t=0)=>t*n:(t=0)=>h-t*n;return Array.from(t.variantChildren).sort(io).forEach((t,n)=>{t.notify("AnimationStart",e),o.push(ia(t,e,{...a,delay:i+(u?0:s)+c(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s,r,a,o,i)}:()=>Promise.resolve(),{when:o}=n;if(!o)return Promise.all([r(),a(i.delay)]);{let[t,e]="beforeChildren"===o?[r,a]:[a,r];return t().then(()=>e())}}function io(t,e){return t.sortNodePosition(e)}function il(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function ih(t){return"string"==typeof t||Array.isArray(t)}let iu=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ic=["initial",...iu],id=ic.length,ip=[...iu].reverse(),im=iu.length;function ig(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iy(){return{animate:ig(!0),whileInView:ig(),whileHover:ig(),whileTap:ig(),whileDrag:ig(),whileFocus:ig(),exit:ig()}}class iv{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ix extends iv{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>ia(t,e,i)));else if("string"==typeof e)s=ia(t,e,i);else{let n="function"==typeof e?f(t,e,i.custom):e;s=Promise.all(ir(t,n,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iy(),s=!0,n=e=>(i,s)=>{let n=f(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...s}=n;i={...i,...s,...e}}return i};function r(r){let{props:a}=t,o=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<id;t++){let s=ic[t],n=e.props[s];(ih(n)||!1===n)&&(i[s]=n)}return i}(t.parent)||{},l=[],h=new Set,u={},c=1/0;for(let e=0;e<im;e++){var p,m;let f=ip[e],g=i[f],y=void 0!==a[f]?a[f]:o[f],v=ih(y),x=f===r?g.isActive:null;!1===x&&(c=e);let b=y===o[f]&&y!==a[f]&&v;if(b&&s&&t.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...u},!g.isActive&&null===x||!y&&!g.prevProp||d(y)||"boolean"==typeof y)continue;let w=(p=g.prevProp,"string"==typeof(m=y)?m!==p:!!Array.isArray(m)&&!il(m,p)),P=w||f===r&&g.isActive&&!b&&v||e>c&&v,T=!1,S=Array.isArray(y)?y:[y],A=S.reduce(n(f),{});!1===x&&(A={});let{prevResolvedValues:j={}}=g,M={...j,...A},E=e=>{P=!0,h.has(e)&&(T=!0,h.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in M){let e=A[t],i=j[t];if(u.hasOwnProperty(t))continue;let s=!1;(O(e)&&O(i)?il(e,i):e===i)?void 0!==e&&h.has(t)?E(t):g.protectedKeys[t]=!0:null!=e?E(t):h.add(t)}g.prevProp=y,g.prevResolvedValues=A,g.isActive&&(u={...u,...A}),s&&t.blockInitialAnimation&&(P=!1);let C=!(b&&w)||T;P&&C&&l.push(...S.map(t=>({animation:t,options:{type:f}})))}if(h.size){let e={};if("boolean"!=typeof a.initial){let i=f(t,Array.isArray(a.initial)?a.initial[0]:a.initial);i&&i.transition&&(e.transition=i.transition)}h.forEach(i=>{let s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=s??null}),l.push({animation:e})}let g=!!l.length;return s&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(g=!1),s=!1,g?e(l):Promise.resolve()}return{animateChanges:r,setActive:function(e,s){if(i[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,s)),i[e].isActive=s;let n=r(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iy(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();d(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ib=0;class iw extends iv{constructor(){super(...arguments),this.id=ib++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let iP={x:!1,y:!1};function iT(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}let iS=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iA(t){return{point:{x:t.pageX,y:t.pageY}}}let ij=t=>e=>iS(e)&&t(e,iA(e));function iM(t,e,i,s){return iT(t,e,ij(i),s)}function iE({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function iC(t){return t.max-t.min}function iV(t,e,i,s=.5){t.origin=s,t.originPoint=tL(e.min,e.max,t.origin),t.scale=iC(i)/iC(e),t.translate=tL(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function ik(t,e,i,s){iV(t.x,e.x,i.x,s?s.originX:void 0),iV(t.y,e.y,i.y,s?s.originY:void 0)}function iD(t,e,i){t.min=i.min+e.min,t.max=t.min+iC(e)}function iR(t,e,i){t.min=e.min-i.min,t.max=t.min+iC(e)}function iL(t,e,i){iR(t.x,e.x,i.x),iR(t.y,e.y,i.y)}let iN=()=>({translate:0,scale:1,origin:0,originPoint:0}),iF=()=>({x:iN(),y:iN()}),iB=()=>({min:0,max:0}),iO=()=>({x:iB(),y:iB()});function iI(t){return[t("x"),t("y")]}function iU(t){return void 0===t||1===t}function iW({scale:t,scaleX:e,scaleY:i}){return!iU(t)||!iU(e)||!iU(i)}function i$(t){return iW(t)||iz(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iz(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iH(t,e,i,s,n){return void 0!==n&&(t=s+n*(t-s)),s+i*(t-s)+e}function iY(t,e=0,i=1,s,n){t.min=iH(t.min,e,i,s,n),t.max=iH(t.max,e,i,s,n)}function iq(t,{x:e,y:i}){iY(t.x,e.translate,e.scale,e.originPoint),iY(t.y,i.translate,i.scale,i.originPoint)}function iX(t,e){t.min=t.min+e,t.max=t.max+e}function i_(t,e,i,s,n=.5){let r=tL(t.min,t.max,n);iY(t,e,i,r,s)}function iK(t,e){i_(t.x,e.x,e.scaleX,e.scale,e.originX),i_(t.y,e.y,e.scaleY,e.scale,e.originY)}function iG(t,e){return iE(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let iZ=({current:t})=>t?t.ownerDocument.defaultView:null;function iJ(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iQ=(t,e)=>Math.abs(t-e);class i0{constructor(t,e,{transformPagePoint:i,contextWindow:s=window,dragSnapToOrigin:n=!1,distanceThreshold:r=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=i5(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iQ(t.x,e.x)**2+iQ(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:s}=t,{timestamp:n}=S;this.history.push({...s,timestamp:n});let{onStart:r,onMove:a}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=i1(e,this.transformPagePoint),P.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=i5("pointercancel"===t.type?this.lastMoveEventInfo:i1(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!iS(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=r,this.contextWindow=s||window;let a=i1(iA(t),this.transformPagePoint),{point:o}=a,{timestamp:l}=S;this.history=[{...o,timestamp:l}];let{onSessionStart:h}=e;h&&h(t,i5(a,this.history)),this.removeListeners=H(iM(this.contextWindow,"pointermove",this.handlePointerMove),iM(this.contextWindow,"pointerup",this.handlePointerUp),iM(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),T(this.updatePoint)}}function i1(t,e){return e?{point:e(t.point)}:t}function i2(t,e){return{x:t.x-e.x,y:t.y-e.y}}function i5({point:t},e){return{point:t,delta:i2(t,i4(e)),offset:i2(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,n=i4(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>q(.1)));)i--;if(!s)return{x:0,y:0};let r=X(n.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let a={x:(n.x-s.x)/r,y:(n.y-s.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function i4(t){return t[t.length-1]}function i3(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function i8(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function i9(t,e,i){return{min:i6(t,e),max:i6(t,i)}}function i6(t,e){return"number"==typeof t?t:t[e]||0}let i7=new WeakMap;class st{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iO(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:s}=this.visualElement;if(s&&!1===s.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new i0(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iA(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:n}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(iP[t])return null;else return iP[t]=!0,()=>{iP[t]=!1};return iP.x||iP.y?null:(iP.x=iP.y=!0,()=>{iP.x=iP.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iI(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tg.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=iC(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&P.postRender(()=>n(t,e)),U(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>iI(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,distanceThreshold:i,contextWindow:iZ(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,s=e||this.latestPanInfo,n=this.isDragging;if(this.cancel(),!n||!s||!i)return;let{velocity:r}=s;this.startAnimation(r);let{onDragEnd:a}=this.getProps();a&&P.postRender(()=>a(i,s))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!se(t,s,this.currentDirection))return;let n=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?tL(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?tL(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&iJ(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:s,right:n}){return{x:i3(t.x,i,n),y:i3(t.y,e,s)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i9(t,"left","right"),y:i9(t,"top","bottom")}}(e),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iI(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iJ(e))return!1;let s=e.current;G(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(t,e,i){let s=iG(t,i),{scroll:n}=e;return n&&(iX(s.x,n.offset.x),iX(s.y,n.offset.y)),s}(s,n.root,this.visualElement.getTransformPagePoint()),a=(t=n.layout.layoutBox,{x:i8(t.x,r.x),y:i8(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=iE(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(iI(a=>{if(!se(a,e,this.currentDirection))return;let l=o&&o[a]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[a]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(a,h)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return U(this.visualElement,t),i.start(is(t,i,0,e,this.visualElement,!1))}stopAnimation(){iI(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iI(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iI(e=>{let{drag:i}=this.getProps();if(!se(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];n.set(t[e]-tL(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iJ(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};iI(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=iC(t),n=iC(e);return n>s?i=ep(e.min,e.max-s,t.min):s>n&&(i=ep(t.min,t.max-n,e.min)),Y(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iI(e=>{if(!se(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];i.set(tL(n,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;i7.set(this.visualElement,this);let t=iM(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iJ(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),P.read(e);let n=iT(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iI(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:r,dragMomentum:a}}}function se(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class si extends iv{constructor(t){super(t),this.removeGroupControls=y,this.removeListeners=y,this.controls=new st(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||y}unmount(){this.removeGroupControls(),this.removeListeners()}}let ss=t=>(e,i)=>{t&&P.postRender(()=>t(e,i))};class sn extends iv{constructor(){super(...arguments),this.removePointerDownListener=y}onPointerDown(t){this.session=new i0(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iZ(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:ss(t),onStart:ss(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&P.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=iM(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let{schedule:sr}=w(queueMicrotask,!1),sa=(0,o.createContext)(null),so=(0,o.createContext)({}),sl=(0,o.createContext)({}),sh={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function su(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let sc={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!ty.test(t))return t;else t=parseFloat(t);let i=su(t,e.target.x),s=su(t,e.target.y);return`${i}% ${s}%`}},sd={},sp=!1;class sm extends o.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;for(let t in sg)sd[t]=sg[t],J(t)&&(sd[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),sp&&n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),sh.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,{projection:r}=i;return r&&(r.isPresent=n,sp=!0,s||t.layoutDependency!==e||void 0===e||t.isPresent!==n?r.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?r.promote():r.relegate()||P.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),sr.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function sf(t){let[e,i]=function(t=!0){let e=(0,o.useContext)(sa);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:s,register:n}=e,r=(0,o.useId)();(0,o.useEffect)(()=>{if(t)return n(r)},[t]);let a=(0,o.useCallback)(()=>t&&s&&s(r),[r,s,t]);return!i&&s?[!1,a]:[!0]}(),s=(0,o.useContext)(so);return(0,a.jsx)(sm,{...t,layoutGroup:s,switchLayoutGroup:(0,o.useContext)(sl),isPresent:e,safeToRemove:i})}let sg={borderRadius:{...sc,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:sc,borderTopRightRadius:sc,borderBottomLeftRadius:sc,borderBottomRightRadius:sc,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=tk.parse(t);if(s.length>5)return t;let n=tk.createTransformer(t),r=+("number"!=typeof s[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;s[0+r]/=a,s[1+r]/=o;let l=tL(a,o,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),n(s)}}};function sy(t){return e2(t)&&"ownerSVGElement"in t}let sv=(t,e)=>t.depth-e.depth;class sx{constructor(){this.children=[],this.isDirty=!1}add(t){C(this.children,t),this.isDirty=!0}remove(t){V(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(sv),this.isDirty=!1,this.children.forEach(t)}}function sb(t){return I(t)?t.get():t}let sw=["TopLeft","TopRight","BottomLeft","BottomRight"],sP=sw.length,sT=t=>"string"==typeof t?parseFloat(t):t,sS=t=>"number"==typeof t||ty.test(t);function sA(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let sj=sE(0,.5,eo),sM=sE(.5,.95,y);function sE(t,e,i){return s=>s<t?0:s>e?1:i(ep(t,e,s))}function sC(t,e){t.min=e.min,t.max=e.max}function sV(t,e){sC(t.x,e.x),sC(t.y,e.y)}function sk(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function sD(t,e,i,s,n){return t-=e,t=s+1/i*(t-s),void 0!==n&&(t=s+1/n*(t-s)),t}function sR(t,e,[i,s,n],r,a){!function(t,e=0,i=1,s=.5,n,r=t,a=t){if(tg.test(e)&&(e=parseFloat(e),e=tL(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=tL(r.min,r.max,s);t===r&&(o-=e),t.min=sD(t.min,e,i,o,n),t.max=sD(t.max,e,i,o,n)}(t,e[i],e[s],e[n],e.scale,r,a)}let sL=["x","scaleX","originX"],sN=["y","scaleY","originY"];function sF(t,e,i,s){sR(t.x,e,sL,i?i.x:void 0,s?s.x:void 0),sR(t.y,e,sN,i?i.y:void 0,s?s.y:void 0)}function sB(t){return 0===t.translate&&1===t.scale}function sO(t){return sB(t.x)&&sB(t.y)}function sI(t,e){return t.min===e.min&&t.max===e.max}function sU(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function sW(t,e){return sU(t.x,e.x)&&sU(t.y,e.y)}function s$(t){return iC(t.x)/iC(t.y)}function sz(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class sH{constructor(){this.members=[]}add(t){C(this.members,t),t.scheduleRender()}remove(t){if(V(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let sY={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},sq=["","X","Y","Z"],sX=0;function s_(t,e,i,s){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function sK({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=sX++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,b.value&&(sY.nodes=sY.calculatedTargetDeltas=sY.calculatedProjections=0),this.nodes.forEach(sJ),this.nodes.forEach(s3),this.nodes.forEach(s8),this.nodes.forEach(sQ),b.addProjectionMetrics&&b.addProjectionMetrics(sY)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new sx)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new k),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=sy(e)&&!(sy(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:s,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let i,s=0,n=()=>this.root.updateBlockedByResize=!1;P.read(()=>{s=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==s&&(s=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=R.now(),s=({timestamp:n})=>{let r=n-i;r>=250&&(T(s),t(r-e))};return P.setup(s,!0),()=>T(s)}(n,250),sh.hasAnimatedSinceResize&&(sh.hasAnimatedSinceResize=!1,this.nodes.forEach(s4)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||n.getDefaultTransition()||ni,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=n.getProps(),l=!this.targetLayout||!sW(this.targetLayout,s),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...g(r,"layout"),onPlay:a,onComplete:o};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||s4(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),T(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(s9),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[$];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",P,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(s1);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(s2);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(s5),this.nodes.forEach(sG),this.nodes.forEach(sZ)):this.nodes.forEach(s2),this.clearAllSnapshots();let t=R.now();S.delta=Y(0,1e3/60,t-S.timestamp),S.timestamp=t,S.isProcessing=!0,A.update.process(S),A.preRender.process(S),A.render.process(S),S.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,sr.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(s0),this.sharedNodes.forEach(s6)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,P.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){P.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iC(this.snapshot.measuredBox.x)||iC(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iO(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sO(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&this.instance&&(e||i$(this.latestValues)||r)&&(n(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),nr((e=s).x),nr(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iO();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(no))){let{scroll:t}=this.root;t&&(iX(e.x,t.offset.x),iX(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iO();if(sV(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:n,options:r}=s;s!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&sV(e,t),iX(e.x,n.offset.x),iX(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=iO();sV(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&iK(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),i$(s.latestValues)&&iK(i,s.latestValues)}return i$(this.latestValues)&&iK(i,this.latestValues),i}removeTransform(t){let e=iO();sV(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!i$(i.latestValues))continue;iW(i.latestValues)&&i.updateSnapshot();let s=iO();sV(s,i.measurePageBox()),sF(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return i$(this.latestValues)&&sF(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==S.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:n}=this.options;if(this.layout&&(s||n)){if(this.resolvedRelativeTargetAt=S.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iO(),this.relativeTargetOrigin=iO(),iL(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sV(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iO(),this.targetWithTransforms=iO()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,a,o;this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,o=this.relativeParent.target,iD(r.x,a.x,o.x),iD(r.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sV(this.target,this.layout.layoutBox),iq(this.target,this.targetDelta)):sV(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iO(),this.relativeTargetOrigin=iO(),iL(this.relativeTargetOrigin,this.target,t.target),sV(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}b.value&&sY.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iW(this.parent.latestValues)||iz(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===S.timestamp&&(i=!1),i)return;let{layout:s,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||n))return;sV(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,s=!1){let n,r,a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){r=(n=i[o]).projectionDelta;let{visualElement:a}=n.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iK(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,iq(t,r)),s&&i$(n.latestValues)&&iK(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iO());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sk(this.prevProjectionDelta.x,this.projectionDelta.x),sk(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),ik(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&sz(this.projectionDelta.x,this.prevProjectionDelta.x)&&sz(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),b.value&&sY.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iF(),this.projectionDelta=iF(),this.projectionDeltaWithTransform=iF()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},a=iF();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=iO(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,c=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(ne));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(s7(a.x,t.x,s),s7(a.y,t.y,s),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,d,p,m,f,g;iL(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,g=s,nt(p.x,m.x,f.x,g),nt(p.y,m.y,f.y,g),i&&(h=this.relativeTarget,d=i,sI(h.x,d.x)&&sI(h.y,d.y))&&(this.isProjectionDirty=!1),i||(i=iO()),sV(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,n,r){n?(t.opacity=tL(0,i.opacity??1,sj(s)),t.opacityExit=tL(e.opacity??1,0,sM(s))):r&&(t.opacity=tL(e.opacity??1,i.opacity??1,s));for(let n=0;n<sP;n++){let r=`border${sw[n]}Radius`,a=sA(e,r),o=sA(i,r);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||sS(a)===sS(o)?(t[r]=Math.max(tL(sT(a),sT(o),s),0),(tg.test(o)||tg.test(a))&&(t[r]+="%")):t[r]=o)}(e.rotate||i.rotate)&&(t.rotate=tL(e.rotate||0,i.rotate||0,s))}(r,n,this.latestValues,s,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(T(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=P.update(()=>{sh.hasAnimatedSinceResize=!0,_.layout++,this.motionValue||(this.motionValue=B(0)),this.currentAnimation=function(t,e,i){let s=I(t)?t:B(t);return s.start(is("",s,e,i)),s.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{_.layout--},onComplete:()=>{_.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&na(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||iO();let e=iC(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=iC(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}sV(e,i),iK(e,n),ik(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new sH),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&s_("z",t,s,this.animationValues);for(let e=0;e<sq.length;e++)s_(`rotate${sq[e]}`,t,s,this.animationValues),s_(`skew${sq[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=sb(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=sb(e?.pointerEvents)||""),this.hasProjected&&!i$(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let n=s.animationValues||s.latestValues;this.applyTransformsToTarget();let r=function(t,e,i){let s="",n=t.x.translate/e.x,r=t.y.translate/e.y,a=i?.z||0;if((n||r||a)&&(s=`translate3d(${n}px, ${r}px, ${a}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:a,skewY:o}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),r&&(s+=`rotateY(${r}deg) `),a&&(s+=`skewX(${a}deg) `),o&&(s+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(s+=`scale(${o}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,n);i&&(r=i(n,r)),t.transform=r;let{x:a,y:o}=this.projectionDelta;for(let e in t.transformOrigin=`${100*a.origin}% ${100*o.origin}% 0`,s.animationValues?t.opacity=s===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:t.opacity=s===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,sd){if(void 0===n[e])continue;let{correct:i,applyTo:a,isCSSVariable:o}=sd[e],l="none"===r?n[e]:i(n[e],s);if(a){let e=a.length;for(let i=0;i<e;i++)t[a[i]]=l}else o?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=s===this?sb(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(s1),this.root.sharedNodes.clear()}}}function sG(t){t.updateLayout()}function sZ(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=t.layout,{animationType:n}=t.options,r=e.source!==t.layout.source;"size"===n?iI(t=>{let s=r?e.measuredBox[t]:e.layoutBox[t],n=iC(s);s.min=i[t].min,s.max=s.min+n}):na(n,e.layoutBox,i)&&iI(s=>{let n=r?e.measuredBox[s]:e.layoutBox[s],a=iC(i[s]);n.max=n.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+a)});let a=iF();ik(a,i,e.layoutBox);let o=iF();r?ik(o,t.applyTransform(s,!0),e.measuredBox):ik(o,i,e.layoutBox);let l=!sO(a),h=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:n,layout:r}=s;if(n&&r){let a=iO();iL(a,e.layoutBox,n.layoutBox);let o=iO();iL(o,i,r.layoutBox),sW(a,o)||(h=!0),s.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function sJ(t){b.value&&sY.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function sQ(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function s0(t){t.clearSnapshot()}function s1(t){t.clearMeasurements()}function s2(t){t.isLayoutDirty=!1}function s5(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function s4(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function s3(t){t.resolveTargetDelta()}function s8(t){t.calcProjection()}function s9(t){t.resetSkewAndRotation()}function s6(t){t.removeLeadSnapshot()}function s7(t,e,i){t.translate=tL(e.translate,0,i),t.scale=tL(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function nt(t,e,i,s){t.min=tL(e.min,i.min,s),t.max=tL(e.max,i.max,s)}function ne(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let ni={duration:.45,ease:[.4,0,.1,1]},ns=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nn=ns("applewebkit/")&&!ns("chrome/")?Math.round:y;function nr(t){t.min=nn(t.min),t.max=nn(t.max)}function na(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(s$(e)-s$(i)))}function no(t){return t!==t.root&&t.scroll?.wasRoot}let nl=sK({attachResizeListener:(t,e)=>iT(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nh={current:void 0},nu=sK({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!nh.current){let t=new nl({});t.mount(window),t.setOptions({layoutScroll:!0}),nh.current=t}return nh.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function nc(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function nd(t){return!("touch"===t.pointerType||iP.x||iP.y)}function np(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=s["onHover"+i];n&&P.postRender(()=>n(e,iA(e)))}class nm extends iv{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=nc(t,i),a=t=>{if(!nd(t))return;let{target:i}=t,s=e(i,t);if("function"!=typeof s||!i)return;let r=t=>{nd(t)&&(s(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,n)};return s.forEach(t=>{t.addEventListener("pointerenter",a,n)}),r}(t,(t,e)=>(np(this.node,e,"Start"),t=>np(this.node,t,"End"))))}unmount(){}}class nf extends iv{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=H(iT(this.node.current,"focus",()=>this.onFocus()),iT(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let ng=(t,e)=>!!e&&(t===e||ng(t,e.parentElement)),ny=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),nv=new WeakSet;function nx(t){return e=>{"Enter"===e.key&&t(e)}}function nb(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let nw=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=nx(()=>{if(nv.has(i))return;nb(i,"down");let t=nx(()=>{nb(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>nb(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function nP(t){return iS(t)&&!(iP.x||iP.y)}function nT(t,e,i){let{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=s["onTap"+("End"===i?"":i)];n&&P.postRender(()=>n(e,iA(e)))}class nS extends iv{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=nc(t,i),a=t=>{let s=t.currentTarget;if(!nP(t))return;nv.add(s);let r=e(s,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),nv.has(s)&&nv.delete(s),nP(t)&&"function"==typeof r&&r(t,{success:e})},o=t=>{a(t,s===window||s===document||i.useGlobalTarget||ng(s,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,n),window.addEventListener("pointercancel",l,n)};return s.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,n),e5(t))&&(t.addEventListener("focus",t=>nw(t,n)),ny.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(nT(this.node,e,"Start"),(t,{success:e})=>nT(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nA=new WeakMap,nj=new WeakMap,nM=t=>{let e=nA.get(t.target);e&&e(t)},nE=t=>{t.forEach(nM)},nC={some:0,all:1};class nV extends iv{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:nC[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;nj.has(i)||nj.set(i,{});let s=nj.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(nE,{root:t,...e})),s[n]}(e);return nA.set(t,i),s.observe(t),()=>{nA.delete(t),s.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),r=e?i:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nk=(0,o.createContext)({strict:!1}),nD=(0,o.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),nR=(0,o.createContext)({});function nL(t){return d(t.animate)||ic.some(e=>ih(t[e]))}function nN(t){return!!(nL(t)||t.variants)}function nF(t){return Array.isArray(t)?t.join(" "):t}let nB="undefined"!=typeof window,nO={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nI={};for(let t in nO)nI[t]={isEnabled:e=>nO[t].some(t=>!!e[t])};let nU=Symbol.for("motionComponentSymbol"),nW=nB?o.useLayoutEffect:o.useEffect;function n$(t,{layout:e,layoutId:i}){return M.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!sd[t]||"opacity"===t)}let nz=(t,e)=>e&&"number"==typeof t?e.transform(t):t,nH={...ti,transform:Math.round},nY={borderWidth:ty,borderTopWidth:ty,borderRightWidth:ty,borderBottomWidth:ty,borderLeftWidth:ty,borderRadius:ty,radius:ty,borderTopLeftRadius:ty,borderTopRightRadius:ty,borderBottomRightRadius:ty,borderBottomLeftRadius:ty,width:ty,maxWidth:ty,height:ty,maxHeight:ty,top:ty,right:ty,bottom:ty,left:ty,padding:ty,paddingTop:ty,paddingRight:ty,paddingBottom:ty,paddingLeft:ty,margin:ty,marginTop:ty,marginRight:ty,marginBottom:ty,marginLeft:ty,backgroundPositionX:ty,backgroundPositionY:ty,rotate:tf,rotateX:tf,rotateY:tf,rotateZ:tf,scale:tn,scaleX:tn,scaleY:tn,scaleZ:tn,skew:tf,skewX:tf,skewY:tf,distance:ty,translateX:ty,translateY:ty,translateZ:ty,x:ty,y:ty,z:ty,perspective:ty,transformPerspective:ty,opacity:ts,originX:tb,originY:tb,originZ:ty,zIndex:nH,fillOpacity:ts,strokeOpacity:ts,numOctaves:nH},nq={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nX=j.length;function n_(t,e,i){let{style:s,vars:n,transformOrigin:r}=t,a=!1,o=!1;for(let t in e){let i=e[t];if(M.has(t)){a=!0;continue}if(J(t)){n[t]=i;continue}{let e=nz(i,nY[t]);t.startsWith("origin")?(o=!0,r[t]=e):s[t]=e}}if(!e.transform&&(a||i?s.transform=function(t,e,i){let s="",n=!0;for(let r=0;r<nX;r++){let a=j[r],o=t[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let t=nz(o,nY[a]);if(!l){n=!1;let e=nq[a]||a;s+=`${e}(${t}) `}i&&(e[a]=t)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),o){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;s.transformOrigin=`${t} ${e} ${i}`}}let nK=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nG(t,e,i){for(let s in e)I(e[s])||n$(s,i)||(t[s]=e[s])}let nZ={offset:"stroke-dashoffset",array:"stroke-dasharray"},nJ={offset:"strokeDashoffset",array:"strokeDasharray"};function nQ(t,{attrX:e,attrY:i,attrScale:s,pathLength:n,pathSpacing:r=1,pathOffset:a=0,...o},l,h,u){if(n_(t,o,h),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=u?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==s&&(c.scale=s),void 0!==n&&function(t,e,i=1,s=0,n=!0){t.pathLength=1;let r=n?nZ:nJ;t[r.offset]=ty.transform(-s);let a=ty.transform(e),o=ty.transform(i);t[r.array]=`${a} ${o}`}(c,n,r,a,!1)}let n0=()=>({...nK(),attrs:{}}),n1=t=>"string"==typeof t&&"svg"===t.toLowerCase(),n2=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function n5(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||n2.has(t)}let n4=t=>!n5(t);try{!function(t){"function"==typeof t&&(n4=e=>e.startsWith("on")?!n5(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let n3=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function n8(t){if("string"!=typeof t||t.includes("-"));else if(n3.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let n9=t=>(e,i)=>{let s=(0,o.useContext)(nR),n=(0,o.useContext)(sa),r=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,s,n){return{latestValues:function(t,e,i,s){let n={},r=s(t,{});for(let t in r)n[t]=sb(r[t]);let{initial:a,animate:o}=t,l=nL(t),h=nN(t);e&&h&&!l&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===o&&(o=e.animate));let u=!!i&&!1===i.initial,c=(u=u||!1===a)?o:a;if(c&&"boolean"!=typeof c&&!d(c)){let e=Array.isArray(c)?c:[c];for(let i=0;i<e.length;i++){let s=m(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=u?e.length-1:0;e=e[t]}null!==e&&(n[t]=e)}for(let e in t)n[e]=t[e]}}}return n}(i,s,n,t),renderState:e()}})(t,e,s,n);return i?r():function(t){let e=(0,o.useRef)(null);return null===e.current&&(e.current=t()),e.current}(r)};function n6(t,e,i){let{style:s}=t,n={};for(let r in s)(I(s[r])||e.style&&I(e.style[r])||n$(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(n[r]=s[r]);return n}let n7={useVisualState:n9({scrapeMotionValuesFromProps:n6,createRenderState:nK})};function rt(t,e,i){let s=n6(t,e,i);for(let i in t)(I(t[i])||I(e[i]))&&(s[-1!==j.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let re={useVisualState:n9({scrapeMotionValuesFromProps:rt,createRenderState:n0})},ri=t=>e=>e.test(t),rs=[ti,ty,tg,tf,tx,tv,{test:t=>"auto"===t,parse:t=>t}],rn=t=>rs.find(ri(t)),rr=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ra=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ro=t=>/^0[^.\s]+$/u.test(t),rl=new Set(["brightness","contrast","saturate","opacity"]);function rh(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(ta)||[];if(!s)return t;let n=i.replace(s,""),r=+!!rl.has(e);return s!==i&&(r*=100),e+"("+r+n+")"}let ru=/\b([a-z-]*)\(.*?\)/gu,rc={...tk,getAnimatableNone:t=>{let e=t.match(ru);return e?e.map(rh).join(" "):t}},rd={...nY,color:tP,backgroundColor:tP,outlineColor:tP,fill:tP,stroke:tP,borderColor:tP,borderTopColor:tP,borderRightColor:tP,borderBottomColor:tP,borderLeftColor:tP,filter:rc,WebkitFilter:rc},rp=t=>rd[t];function rm(t,e){let i=rp(t);return i!==rc&&(i=tk),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let rf=new Set(["auto","none","0"]);class rg extends ez{constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&tt(s=s.trim())){let n=function t(e,i,s=1){G(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[n,r]=function(t){let e=ra.exec(t);if(!e)return[,];let[,i,s,n]=e;return[`--${i??s}`,n]}(e);if(!n)return;let a=window.getComputedStyle(i).getPropertyValue(n);if(a){let t=a.trim();return rr(t)?parseFloat(t):t}return tt(r)?t(r,i,s+1):r}(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!E.has(i)||2!==t.length)return;let[s,n]=t,r=rn(s),a=rn(n);if(r!==a)if(eR(r)&&eR(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eF[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;(null===t[e]||("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||ro(s)))&&i.push(e)}i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){let e=t[n];"string"==typeof e&&!rf.has(e)&&tM(e).values.length&&(s=t[n]),n++}if(s&&i)for(let n of e)t[n]=rm(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eF[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);let n=i.length-1,r=i[n];i[n]=eF[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let ry=[...rs,tP,tk],rv=t=>ry.find(ri(t)),rx={current:null},rb={current:!1},rw=new WeakMap,rP=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class rT{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ez,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=R.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,P.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!n,this.isControllingVariants=nL(e),this.isVariantNode=nN(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in u){let e=u[t];void 0!==o[t]&&I(e)&&e.set(o[t],!1)}}mount(t){this.current=t,rw.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),rb.current||function(){if(rb.current=!0,nB)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>rx.current=t.matches;t.addEventListener("change",e),e()}else rx.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||rx.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),T(this.notifyUpdate),T(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=M.has(t);s&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&P.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nI){let e=nI[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iO()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<rP.length;e++){let i=rP[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let n=e[s],r=i[s];if(I(n))t.addValue(s,n);else if(I(r))t.addValue(s,B(n,{owner:t}));else if(r!==n)if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(s);t.addValue(s,B(void 0!==e?e:n,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=B(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(rr(i)||ro(i))?i=parseFloat(i):!rv(i)&&tk.test(e)&&(i=rm(t,e)),this.setBaseTarget(t,I(i)?i.get():i)),I(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=m(this.props,i,this.presenceContext?.custom);s&&(e=s[t])}if(i&&void 0!==e)return e;let s=this.getBaseTargetFromProps(this.props,t);return void 0===s||I(s)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new k),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class rS extends rT{constructor(){super(...arguments),this.KeyframeResolver=rg}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;I(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function rA(t,{style:e,vars:i},s,n){let r,a=t.style;for(r in e)a[r]=e[r];for(r in n?.applyProjectionStyles(a,s),i)a.setProperty(r,i[r])}class rj extends rS{constructor(){super(...arguments),this.type="html",this.renderInstance=rA}readValueFromInstance(t,e){if(M.has(e))return this.projection?.isProjecting?eC(e):ek(t,e);{let i=window.getComputedStyle(t),s=(J(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iG(t,e)}build(t,e,i){n_(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return n6(t,e,i)}}let rM=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class rE extends rS{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iO}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(M.has(e)){let t=rp(e);return t&&t.default||0}return e=rM.has(e)?e:W(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return rt(t,e,i)}build(t,e,i){nQ(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,s){for(let i in rA(t,e,void 0,s),e.attrs)t.setAttribute(rM.has(i)?i:W(i),e.attrs[i])}mount(t){this.isSVGTag=n1(t.tagName),super.mount(t)}}let rC=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((n={animation:{Feature:ix},exit:{Feature:iw},inView:{Feature:nV},tap:{Feature:nS},focus:{Feature:nf},hover:{Feature:nm},pan:{Feature:sn},drag:{Feature:si,ProjectionNode:nu,MeasureLayout:sf},layout:{ProjectionNode:nu,MeasureLayout:sf}},r=(t,e)=>n8(t)?new rE(e):new rj(e,{allowProjection:t!==o.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:s,Component:n}){function r(t,r){var l,h,u;let c,d={...(0,o.useContext)(nD),...t,layoutId:function({layoutId:t}){let e=(0,o.useContext)(so).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:p}=d,m=function(t){let{initial:e,animate:i}=function(t,e){if(nL(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ih(e)?e:void 0,animate:ih(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,o.useContext)(nR));return(0,o.useMemo)(()=>({initial:e,animate:i}),[nF(e),nF(i)])}(t),f=s(t,p);if(!p&&nB){h=0,u=0,(0,o.useContext)(nk).strict;let t=function(t){let{drag:e,layout:i}=nI;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(d);c=t.MeasureLayout,m.visualElement=function(t,e,i,s,n){let{visualElement:r}=(0,o.useContext)(nR),a=(0,o.useContext)(nk),l=(0,o.useContext)(sa),h=(0,o.useContext)(nD).reducedMotion,u=(0,o.useRef)(null);s=s||a.renderer,!u.current&&s&&(u.current=s(t,{visualState:e,parent:r,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:h}));let c=u.current,d=(0,o.useContext)(sl);c&&!c.projection&&n&&("html"===c.type||"svg"===c.type)&&function(t,e,i,s){let{layoutId:n,layout:r,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!a||o&&iJ(o),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:h})}(u.current,i,n,d);let p=(0,o.useRef)(!1);(0,o.useInsertionEffect)(()=>{c&&p.current&&c.update(i,l)});let m=i[$],f=(0,o.useRef)(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return nW(()=>{c&&(p.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),sr.render(c.render),f.current&&c.animationState&&c.animationState.animateChanges())}),(0,o.useEffect)(()=>{c&&(!f.current&&c.animationState&&c.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1))}),c}(n,f,d,e,t.ProjectionNode)}return(0,a.jsxs)(nR.Provider,{value:m,children:[c&&m.visualElement?(0,a.jsx)(c,{visualElement:m.visualElement,...d}):null,i(n,t,(l=m.visualElement,(0,o.useCallback)(t=>{t&&f.onMount&&f.onMount(t),l&&(t?l.mount(t):l.unmount()),r&&("function"==typeof r?r(t):iJ(r)&&(r.current=t))},[l])),f,p,m.visualElement)]})}t&&function(t){for(let e in t)nI[e]={...nI[e],...t[e]}}(t),r.displayName=`motion.${"string"==typeof n?n:`create(${n.displayName??n.name??""})`}`;let l=(0,o.forwardRef)(r);return l[nU]=n,l}({...n8(t)?re:n7,preloadedFeatures:n,useRender:function(t=!1){return(e,i,s,{latestValues:n},r)=>{let a=(n8(e)?function(t,e,i,s){let n=(0,o.useMemo)(()=>{let i=n0();return nQ(i,e,n1(s),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};nG(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return nG(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,o.useMemo)(()=>{let i=nK();return n_(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,n,r,e),l=function(t,e,i){let s={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(n4(n)||!0===i&&n5(n)||!e&&!n5(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}(i,"string"==typeof e,t),h=e!==o.Fragment?{...l,...a,ref:s}:{},{children:u}=i,c=(0,o.useMemo)(()=>I(u)?u.get():u,[u]);return(0,o.createElement)(e,{...h,children:c})}}(e),createVisualElement:r,Component:t})}));function rV({videoSrc:t,headline:e="Premium Cannabis",subheadline:i="Discover Colorado's finest selection of cannabis products, expertly curated for quality, potency, and purity.",ctas:s=["Shop Products","Find Locations"]}){return(0,a.jsxs)("section",{className:"relative h-screen overflow-hidden bg-gradient-to-br from-primary-800 via-primary-700 to-primary-600 text-cream-50",children:[t&&(0,a.jsx)("video",{autoPlay:!0,muted:!0,loop:!0,playsInline:!0,className:"absolute w-full h-full object-cover opacity-30",children:(0,a.jsx)("source",{src:t,type:"video/mp4"})}),!t&&(0,a.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,a.jsx)("div",{className:"absolute inset-0 bg-primary-900"})}),(0,a.jsxs)("div",{className:"relative z-10 flex flex-col items-center justify-center h-full text-center px-6",children:[(0,a.jsxs)(rC.h1,{className:"text-4xl md:text-6xl font-serif font-bold leading-tight mb-6",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:[e,(0,a.jsx)("span",{className:"block text-gold-300",children:"Excellence"})]}),(0,a.jsx)(rC.p,{className:"text-xl md:text-2xl text-cream-200 mb-8 leading-relaxed max-w-4xl",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},children:i}),(0,a.jsxs)(rC.div,{className:"flex flex-col sm:flex-row gap-4 justify-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},children:[(0,a.jsxs)(c(),{href:"/products",className:"inline-flex items-center justify-center px-8 py-4 bg-gold-500 text-primary-800 font-semibold rounded-lg hover:bg-gold-400 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:ring-offset-2 focus:ring-offset-primary-800",children:[s[0]||"Shop Products",(0,a.jsx)("svg",{className:"ml-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]}),(0,a.jsxs)(c(),{href:"/locations",className:"inline-flex items-center justify-center px-8 py-4 bg-transparent text-cream-50 font-semibold rounded-lg border-2 border-cream-50 hover:bg-cream-50 hover:text-primary-800 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cream-50 focus:ring-offset-2 focus:ring-offset-primary-800",children:[s[1]||"Find Locations",(0,a.jsxs)("svg",{className:"ml-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})]})]})]}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 z-20",children:(0,a.jsx)("svg",{viewBox:"0 0 1440 120",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{d:"M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0Z",fill:"#f8f6f0"})})})]})}let rk=[{name:"Premium Flower",description:"Hand-selected, top-shelf cannabis flower with exceptional quality and potency.",image:"/api/placeholder/400/300",href:"/products/flower",features:["Lab Tested","Organic Grown","Various Strains"],color:"from-primary-600 to-primary-700"},{name:"Concentrates & Extracts",description:"Pure, potent concentrates including wax, shatter, and live resin.",image:"/api/placeholder/400/300",href:"/products/concentrates",features:["High Potency","Pure Extraction","Multiple Forms"],color:"from-gold-500 to-gold-600"},{name:"Edibles",description:"Delicious, precisely dosed edibles for a controlled cannabis experience.",image:"/api/placeholder/400/300",href:"/products/edibles",features:["Precise Dosing","Great Taste","Long Lasting"],color:"from-sage-400 to-sage-500"},{name:"Topicals",description:"Therapeutic cannabis topicals for localized relief and wellness.",image:"/api/placeholder/400/300",href:"/products/topicals",features:["Non-Psychoactive","Therapeutic","Natural Relief"],color:"from-cream-400 to-cream-500"}];function rD(){return(0,a.jsx)("section",{className:"py-20 bg-cream-100",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(rC.div,{className:"text-center mb-16",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[(0,a.jsx)("h2",{className:"text-4xl font-serif font-bold text-primary-800 mb-4",children:"Our Product Categories"}),(0,a.jsx)("p",{className:"text-xl text-charcoal-600 max-w-3xl mx-auto leading-relaxed",children:"Explore our carefully curated selection of premium cannabis products, each category offering unique benefits and experiences."})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:rk.map((t,e)=>(0,a.jsxs)(rC.div,{className:"group bg-cream-50 rounded-xl overflow-hidden shadow-soft hover:shadow-medium transition-all duration-300",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},whileHover:{scale:1.05,y:-8},transition:{duration:.3,delay:.1*e},viewport:{once:!0},children:[(0,a.jsxs)("div",{className:`h-48 bg-gradient-to-br ${t.color} relative overflow-hidden`,children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300"}),(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-center text-cream-50",children:(0,a.jsxs)("div",{className:"w-16 h-16 mx-auto mb-2 bg-cream-50 bg-opacity-20 rounded-full flex items-center justify-center",children:[0===e&&(0,a.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})}),1===e&&(0,a.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})}),2===e&&(0,a.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})}),3===e&&(0,a.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"})})]})})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-primary-800 mb-2",children:t.name}),(0,a.jsx)("p",{className:"text-charcoal-600 text-sm mb-4 leading-relaxed",children:t.description}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:t.features.map(t=>(0,a.jsx)("span",{className:"px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full",children:t},t))}),(0,a.jsxs)(c(),{href:t.href,className:"inline-flex items-center text-primary-700 hover:text-primary-800 font-medium text-sm group-hover:underline transition-colors duration-200",children:["Explore ",t.name,(0,a.jsx)("svg",{className:"ml-1 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]},t.name))}),(0,a.jsx)(rC.div,{className:"text-center mt-12",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},children:(0,a.jsxs)(c(),{href:"/products",className:"inline-flex items-center px-8 py-4 bg-primary-800 text-cream-50 font-semibold rounded-lg hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-offset-2",children:["View All Products",(0,a.jsx)("svg",{className:"ml-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})})]})})}var rR=i(5009),rL=i(7499);function rN(){let[t,e]=(0,o.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[!t&&(0,a.jsx)(h,{onVerified:()=>e(!0)}),t&&(0,a.jsxs)("div",{className:"min-h-screen bg-cream-100",children:[(0,a.jsx)(l.A,{}),(0,a.jsxs)("main",{children:[(0,a.jsx)(rV,{}),(0,a.jsx)("section",{className:"py-8 bg-cream-100",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)(rL.A,{})})}),(0,a.jsx)(rD,{}),(0,a.jsx)(rR.A,{}),(0,a.jsx)("section",{className:"py-20 bg-primary-800",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-serif font-bold text-cream-50 mb-4",children:"Stay Updated with Apothecary Extracts"}),(0,a.jsx)("p",{className:"text-xl text-cream-200 mb-8",children:"Get the latest news on new products, special offers, and cannabis education."}),(0,a.jsxs)("form",{className:"flex flex-col sm:flex-row gap-4 max-w-md mx-auto",children:[(0,a.jsx)("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-4 py-3 rounded-lg border border-cream-300 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:border-transparent text-charcoal-800",required:!0}),(0,a.jsx)("button",{type:"submit",className:"px-8 py-3 bg-gold-500 text-primary-800 font-semibold rounded-lg hover:bg-gold-400 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:ring-offset-2 focus:ring-offset-primary-800",children:"Subscribe"})]}),(0,a.jsx)("p",{className:"text-sm text-cream-300 mt-4",children:"We respect your privacy. Unsubscribe at any time."})]})})]}),(0,a.jsx)("footer",{className:"bg-charcoal-800 text-cream-100 py-16",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,a.jsx)("div",{className:"text-2xl font-serif font-bold text-cream-50 mb-4",children:"Apothecary Extracts"}),(0,a.jsx)("p",{className:"text-cream-300 mb-4 leading-relaxed",children:"Colorado's premier cannabis dispensary, committed to providing the highest quality products and exceptional customer service."}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)("a",{href:"#",className:"text-cream-300 hover:text-gold-400 transition-colors",children:[(0,a.jsx)("span",{className:"sr-only",children:"Facebook"}),(0,a.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})]}),(0,a.jsxs)("a",{href:"#",className:"text-cream-300 hover:text-gold-400 transition-colors",children:[(0,a.jsx)("span",{className:"sr-only",children:"Instagram"}),(0,a.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z"})})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-cream-50 mb-4",children:"Quick Links"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/products",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Products"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/locations",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Locations"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/about",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"About Us"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/education",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Education"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-cream-50 mb-4",children:"Legal"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/privacy",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Privacy Policy"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/terms",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Terms of Service"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/compliance",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Compliance"})})]})]})]}),(0,a.jsx)("div",{className:"border-t border-charcoal-600 mt-12 pt-8 text-center",children:(0,a.jsx)("p",{className:"text-cream-400 text-sm",children:"\xa9 2025 Apothecary Extracts. All rights reserved. | Licensed Cannabis Retailer | Must be 21+ to purchase"})})]})})]})]})}},3033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3389:(t,e,i)=>{Promise.resolve().then(i.bind(i,1245))},3873:t=>{"use strict";t.exports=require("path")},5537:(t,e,i)=>{"use strict";i.r(e),i.d(e,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>d,tree:()=>h});var s=i(5239),n=i(8088),r=i(8170),a=i.n(r),o=i(893),l={};for(let t in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(l[t]=()=>o[t]);i.d(e,l);let h={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,1204)),"O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\page.tsx"],metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,4431)),"O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\page.tsx"],c={require:i,loadChunk:()=>Promise.resolve()},d=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}})},8125:(t,e,i)=>{Promise.resolve().then(i.bind(i,1204))},9121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:t=>{"use strict";t.exports=require("url")}};var e=require("../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),s=e.X(0,[447,210,567,540],()=>i(5537));module.exports=s})();