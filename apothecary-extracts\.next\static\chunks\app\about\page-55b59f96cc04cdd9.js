(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[220],{2056:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});var t=a(5155);function r(){return(0,t.jsx)("div",{className:"bg-gold-100 border border-gold-400 rounded-lg p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-gold-600 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-gold-800 mb-2",children:"Important Legal Information"}),(0,t.jsxs)("div",{className:"text-sm text-gold-700 space-y-2",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Colorado State Compliance:"})," This establishment is licensed by the State of Colorado to sell cannabis products to adults 21 years of age and older. License #: [LICENSE_NUMBER]"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Health & Safety:"})," Cannabis products have not been evaluated by the FDA and are not intended to diagnose, treat, cure, or prevent any disease. Keep out of reach of children and pets."]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Consumption Guidelines:"})," Do not operate vehicles or machinery after use. Effects may be delayed with edible products. Start with low doses and wait before consuming more."]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Legal Restrictions:"})," Cannabis products may not be transported across state lines. Consumption is prohibited in public places and federal properties."]})]})]})]})})}},3176:(e,s,a)=>{Promise.resolve().then(a.bind(a,5550))},5506:(e,s,a)=>{"use strict";a.d(s,{A:()=>n});var t=a(5155),r=a(2115),l=a(6874),i=a.n(l);function n(){let[e,s]=(0,r.useState)(!1),a=[{name:"Home",href:"/"},{name:"Products",href:"/products"},{name:"Locations",href:"/locations"},{name:"About",href:"/about"},{name:"Education",href:"/education"},{name:"Contact",href:"/contact"}];return(0,t.jsxs)("nav",{className:"bg-cream-50 shadow-soft sticky top-0 z-40",children:[(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(i(),{href:"/",className:"flex items-center",children:(0,t.jsx)("div",{className:"text-2xl font-serif font-bold text-primary-800",children:"Apothecary Extracts"})})}),(0,t.jsx)("div",{className:"hidden md:block",children:(0,t.jsx)("div",{className:"ml-10 flex items-baseline space-x-8",children:a.map(e=>(0,t.jsx)(i(),{href:e.href,className:"text-charcoal-700 hover:text-primary-800 px-3 py-2 text-sm font-medium transition-colors duration-200",children:e.name},e.name))})}),(0,t.jsx)("div",{className:"md:hidden",children:(0,t.jsxs)("button",{onClick:()=>s(!e),className:"inline-flex items-center justify-center p-2 rounded-md text-charcoal-700 hover:text-primary-800 hover:bg-cream-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-600","aria-expanded":"false",children:[(0,t.jsx)("span",{className:"sr-only",children:"Open main menu"}),e?(0,t.jsx)("svg",{className:"block h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}):(0,t.jsx)("svg",{className:"block h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})]})})]})}),e&&(0,t.jsx)("div",{className:"md:hidden",children:(0,t.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-cream-100 border-t border-cream-200",children:a.map(e=>(0,t.jsx)(i(),{href:e.href,className:"text-charcoal-700 hover:text-primary-800 block px-3 py-2 text-base font-medium transition-colors duration-200",onClick:()=>s(!1),children:e.name},e.name))})})]})}},5550:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>i});var t=a(5155),r=a(5506),l=a(2056);function i(){return(0,t.jsxs)("div",{className:"min-h-screen bg-cream-100",children:[(0,t.jsx)(r.A,{}),(0,t.jsxs)("main",{children:[(0,t.jsx)("section",{className:"bg-gradient-to-br from-primary-800 to-primary-600 text-cream-50 py-16",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-4xl sm:text-5xl font-serif font-bold mb-4",children:"About Apothecary Extracts"}),(0,t.jsx)("p",{className:"text-xl text-cream-200 max-w-3xl mx-auto",children:"Colorado's premier cannabis dispensary, dedicated to quality, education, and exceptional service."})]})})}),(0,t.jsx)("section",{className:"py-8 bg-cream-100",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsx)(l.A,{})})}),(0,t.jsx)("section",{className:"py-16 bg-cream-50",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-3xl font-serif font-bold text-primary-800 mb-6",children:"Our Story"}),(0,t.jsxs)("div",{className:"space-y-4 text-charcoal-700",children:[(0,t.jsx)("p",{children:"Founded in 2020, Apothecary Extracts began with a simple mission: to provide Colorado Springs with the highest quality cannabis products while maintaining the highest standards of safety, compliance, and customer service."}),(0,t.jsx)("p",{children:"Our team of cannabis experts carefully curates every product in our inventory, ensuring that each item meets our strict quality standards. From premium flower to innovative concentrates and precisely dosed edibles, we offer something for every cannabis enthusiast."}),(0,t.jsx)("p",{children:"We believe in the power of education and strive to create an environment where customers feel comfortable asking questions and learning about cannabis. Our knowledgeable budtenders are always ready to help you find the perfect product for your needs."})]})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-br from-primary-100 to-sage-100 rounded-xl p-8 text-center",children:[(0,t.jsx)("div",{className:"w-24 h-24 mx-auto mb-4 bg-primary-800 rounded-full flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-12 h-12 text-gold-300",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})})}),(0,t.jsx)("h3",{className:"text-2xl font-serif font-bold text-primary-800 mb-2",children:"Premium Quality"}),(0,t.jsx)("p",{className:"text-charcoal-700",children:"Every product is lab-tested and carefully selected for quality, potency, and purity."})]})]})})}),(0,t.jsx)("section",{className:"py-16 bg-cream-100",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h2",{className:"text-3xl font-serif font-bold text-primary-800 mb-4",children:"Our Values"}),(0,t.jsx)("p",{className:"text-xl text-charcoal-600 max-w-3xl mx-auto",children:"These core principles guide everything we do at Apothecary Extracts."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-primary-800 rounded-full flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-8 h-8 text-cream-50",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-primary-800 mb-2",children:"Quality First"}),(0,t.jsx)("p",{className:"text-charcoal-600",children:"We never compromise on quality. Every product is rigorously tested and meets our high standards."})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-primary-800 rounded-full flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-8 h-8 text-cream-50",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-primary-800 mb-2",children:"Education"}),(0,t.jsx)("p",{className:"text-charcoal-600",children:"We believe in empowering our customers with knowledge about cannabis and its benefits."})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-primary-800 rounded-full flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-8 h-8 text-cream-50",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM4 18v-6h2.5l6 6H4zm16.5-9.5L19 7l-7.5 7.5L9 12l-2 2 5.5 5.5L22 10l-1.5-1.5z"})})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-primary-800 mb-2",children:"Community"}),(0,t.jsx)("p",{className:"text-charcoal-600",children:"We're committed to being a positive force in the Colorado Springs community."})]})]})]})}),(0,t.jsx)("section",{className:"py-16 bg-cream-50",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h2",{className:"text-3xl font-serif font-bold text-primary-800 mb-4",children:"Our Team"}),(0,t.jsx)("p",{className:"text-xl text-charcoal-600 max-w-3xl mx-auto",children:"Meet the passionate professionals who make Apothecary Extracts special."})]}),(0,t.jsxs)("div",{className:"bg-gold-100 rounded-xl p-8 text-center",children:[(0,t.jsx)("h3",{className:"text-2xl font-serif font-bold text-primary-800 mb-4",children:"Expert Budtenders"}),(0,t.jsx)("p",{className:"text-charcoal-700 mb-6 max-w-2xl mx-auto",children:"Our knowledgeable team members are passionate about cannabis and dedicated to helping you find the perfect products for your needs. They stay up-to-date with the latest industry developments and are always ready to share their expertise."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsxs)("div",{className:"bg-cream-50 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-primary-800 mb-1",children:"Certified Professionals"}),(0,t.jsx)("p",{className:"text-sm text-charcoal-600",children:"All staff are certified and trained in cannabis knowledge"})]}),(0,t.jsxs)("div",{className:"bg-cream-50 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-primary-800 mb-1",children:"Ongoing Education"}),(0,t.jsx)("p",{className:"text-sm text-charcoal-600",children:"Regular training on new products and industry trends"})]})]})]})]})})]}),(0,t.jsx)("footer",{className:"bg-charcoal-800 text-cream-100 py-16",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-serif font-bold text-cream-50 mb-4",children:"Apothecary Extracts"}),(0,t.jsx)("p",{className:"text-cream-300 mb-4",children:"Colorado's premier cannabis dispensary"}),(0,t.jsx)("p",{className:"text-cream-400 text-sm",children:"\xa9 2025 Apothecary Extracts. All rights reserved. | Must be 21+ to purchase"})]})})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[874,441,684,358],()=>s(3176)),_N_E=e.O()}]);